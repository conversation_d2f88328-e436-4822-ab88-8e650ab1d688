<!DOCTYPE html>
<html>
<head>
    <title>Baum Organizations Shortcodes Test</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1, h2 {
            color: #333;
            border-bottom: 2px solid #007cba;
            padding-bottom: 10px;
        }
        .shortcode-example {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 14px;
            color: #495057;
        }
        .section {
            margin: 40px 0;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏢 Baum Organizations Shortcodes Test Page</h1>
        <p>This page demonstrates the various shortcodes available in the Baum Organizations plugin. Copy these examples to your WordPress pages or posts.</p>

        <div class="section">
            <h2>1. Single Company Profile - New Clean Design</h2>
            <p>Display a modern company profile card matching the iOS-style design:</p>

            <div class="shortcode-example">
                [baum_company name="Apple Inc."]
            </div>

            <p><strong>New Features:</strong></p>
            <ul>
                <li>✅ Clean rounded corners (16px border-radius)</li>
                <li>✅ Black logo background containers</li>
                <li>✅ Modern typography and spacing</li>
                <li>✅ Description section with "Read more" links</li>
                <li>✅ About section with clean data layout</li>
                <li>✅ Sample data visualization bars</li>
                <li>✅ iOS-inspired design language</li>
            </ul>

            <h3>Variations:</h3>

            <div class="shortcode-example">
                [baum_company name="Microsoft" style="minimal"]
            </div>
            <p>Clean minimal style with black logo container</p>

            <div class="shortcode-example">
                [baum_company name="Google" width="500px"]
            </div>
            <p>Wider card format</p>
        </div>

        <div class="section">
            <h2>2. Company List</h2>
            <p>Display multiple companies with filtering:</p>
            
            <div class="shortcode-example">
                [baum_company_list limit="5" industry="technology"]
            </div>
            <p><strong>Expected Result:</strong> List of 5 technology companies in minimal format.</p>
            
            <h3>Variations:</h3>
            
            <div class="shortcode-example">
                [baum_company_list country="United States" limit="8" columns="2"]
            </div>
            <p>US companies in 2 columns</p>
            
            <div class="shortcode-example">
                [baum_company_list size="1001-5000" limit="6"]
            </div>
            <p>Medium-sized companies</p>
        </div>

        <div class="section">
            <h2>3. Company Search</h2>
            <p>Interactive search form for visitors:</p>
            
            <div class="shortcode-example">
                [baum_company_search]
            </div>
            <p><strong>Expected Result:</strong> Search form with input field and search button.</p>
            
            <h3>Variations:</h3>
            
            <div class="shortcode-example">
                [baum_company_search placeholder="Find your company..." button_text="Search Now" results_per_page="15"]
            </div>
            <p>Custom placeholder and button text</p>
        </div>

        <div class="section">
            <h2>4. Real-World Examples</h2>
            
            <h3>Company Showcase Page</h3>
            <div class="shortcode-example">
&lt;h2&gt;Featured Technology Companies&lt;/h2&gt;
[baum_company_list industry="Computer Software" limit="6" columns="2"]

&lt;h2&gt;Company Spotlight&lt;/h2&gt;
[baum_company name="Apple Inc." width="600px"]
            </div>
            
            <h3>Company Directory</h3>
            <div class="shortcode-example">
&lt;h2&gt;Find Companies&lt;/h2&gt;
[baum_company_search placeholder="Search by name or industry..." results_per_page="20"]

&lt;h3&gt;Popular Companies&lt;/h3&gt;
[baum_company_list limit="10" columns="2"]
            </div>
            
            <h3>Industry-Specific Pages</h3>
            <div class="shortcode-example">
&lt;!-- Technology Companies --&gt;
[baum_company_list industry="Information Technology" limit="12" columns="3"]

&lt;!-- Marketing Companies --&gt;
[baum_company_list industry="Marketing" limit="8" columns="2"]
            </div>
        </div>

        <div class="section">
            <h2>5. Testing Checklist</h2>
            <p>To test these shortcodes on your WordPress site:</p>
            <ol>
                <li>✅ Activate the Baum Organizations plugin</li>
                <li>✅ Ensure the database has been imported (21,516 organizations)</li>
                <li>✅ Create a new page or post</li>
                <li>✅ Copy any shortcode from above</li>
                <li>✅ Paste it into your content</li>
                <li>✅ Preview or publish the page</li>
                <li>✅ Verify the company information displays correctly</li>
                <li>✅ Check that logos are loading</li>
                <li>✅ Test responsive design on mobile</li>
            </ol>
        </div>

        <div class="section">
            <h2>6. Available Company Data</h2>
            <p>Your database contains 21,516 organizations with the following information:</p>
            <ul>
                <li><strong>All companies have logos</strong> (100% coverage)</li>
                <li><strong>Geographic coverage:</strong> US (5,852), UK (1,835), India (1,087), France (876), Australia (540)</li>
                <li><strong>Top industries:</strong> IT Services (2,458), Software (1,971), Marketing (1,273), Consulting (1,177)</li>
                <li><strong>Data fields:</strong> Name, Website, Industry, Size, Founded, Location, LinkedIn</li>
            </ul>
        </div>

        <div class="section">
            <h2>7. Troubleshooting</h2>
            <h3>If shortcodes don't work:</h3>
            <ul>
                <li>Check that the plugin is activated</li>
                <li>Verify the database exists (Organizations → Database Manager)</li>
                <li>Try exact company names from your database</li>
                <li>Check for PHP errors in WordPress debug log</li>
            </ul>
            
            <h3>If companies aren't found:</h3>
            <ul>
                <li>Use the admin interface to browse available companies</li>
                <li>Try partial name matches (e.g., "Apple" instead of "Apple Inc.")</li>
                <li>Check the database manager for exact company names</li>
            </ul>
        </div>

        <div class="section">
            <h2>8. Next Steps</h2>
            <p>After testing the shortcodes:</p>
            <ol>
                <li>Customize the CSS styling to match your theme</li>
                <li>Create dedicated company directory pages</li>
                <li>Add company profiles to relevant blog posts</li>
                <li>Use the search functionality for user engagement</li>
                <li>Consider creating industry-specific landing pages</li>
            </ol>
        </div>

        <footer style="margin-top: 50px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #666; text-align: center;">
            <p>Baum Organizations Plugin - WordPress Company Database with Logo Integration</p>
        </footer>
    </div>
</body>
</html>

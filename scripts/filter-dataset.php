<?php
/**
 * Filter company dataset to keep only organizations with available logos
 * 
 * This script processes the large company-dataset.json file and creates a filtered
 * version containing only organizations that have matching logo files.
 * 
 * Usage: php filter-dataset.php
 */

// Set memory limit and execution time
ini_set('memory_limit', '1G');
set_time_limit(0);

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define paths
$plugin_dir = dirname(__DIR__);
$input_file = $plugin_dir . '/company-dataset.json';
$output_file = $plugin_dir . '/company-dataset-filtered.json';
$logos_dir = $plugin_dir . '/logos/';

echo "=== Company Dataset Filter Script ===\n";
echo "Input file: $input_file\n";
echo "Output file: $output_file\n";
echo "Logos directory: $logos_dir\n\n";

// Check if input file exists
if (!file_exists($input_file)) {
  die("Error: Input file not found: $input_file\n");
}

// Check if logos directory exists
if (!file_exists($logos_dir)) {
  die("Error: Logos directory not found: $logos_dir\n");
}

// Get input file size
$input_size = filesize($input_file);
echo "Input file size: " . formatBytes($input_size) . "\n";

// Build logo filename cache for fast lookups
echo "Building logo cache...\n";
$logo_cache = buildLogoCache($logos_dir);
echo "Found " . count($logo_cache) . " logo files\n\n";

// Process the dataset
echo "Starting dataset filtering...\n";
$start_time = microtime(true);

$input_handle = fopen($input_file, 'r');
$output_handle = fopen($output_file, 'w');

if (!$input_handle) {
  die("Error: Cannot open input file for reading\n");
}

if (!$output_handle) {
  die("Error: Cannot open output file for writing\n");
}

$total_processed = 0;
$total_matched = 0;
$batch_size = 10000;
$last_progress_time = microtime(true);

while (($line = fgets($input_handle)) !== false) {
  $total_processed++;
  
  // Parse JSON line
  $organization = json_decode(trim($line), true);
  
  if (!$organization || !isset($organization['name'])) {
    continue;
  }
  
  // Check if logo exists
  $logo_filename = findLogoFilename($organization['name'], $logo_cache);
  
  if ($logo_filename) {
    // Add logo filename to the organization data
    $organization['logo_filename'] = $logo_filename;
    
    // Write to output file
    fwrite($output_handle, json_encode($organization) . "\n");
    $total_matched++;
  }
  
  // Show progress every batch
  if ($total_processed % $batch_size === 0) {
    $current_time = microtime(true);
    $elapsed = $current_time - $start_time;
    $rate = $total_processed / $elapsed;
    $match_rate = ($total_matched / $total_processed) * 100;
    
    echo sprintf(
      "Processed: %s | Matched: %s (%.2f%%) | Rate: %.0f/sec | Elapsed: %s\n",
      number_format($total_processed),
      number_format($total_matched),
      $match_rate,
      $rate,
      formatTime($elapsed)
    );
    
    // Check output file size to prevent disk space issues
    $output_size = ftell($output_handle);
    if ($output_size > 1024 * 1024 * 1024) { // 1GB limit
      echo "Warning: Output file approaching 1GB, consider stopping\n";
    }
  }
}

fclose($input_handle);
fclose($output_handle);

$end_time = microtime(true);
$total_time = $end_time - $start_time;

// Final statistics
$output_size = filesize($output_file);
$compression_ratio = (1 - ($output_size / $input_size)) * 100;

echo "\n=== Filtering Complete ===\n";
echo "Total organizations processed: " . number_format($total_processed) . "\n";
echo "Organizations with logos: " . number_format($total_matched) . "\n";
echo "Match rate: " . sprintf("%.2f%%", ($total_matched / $total_processed) * 100) . "\n";
echo "Processing time: " . formatTime($total_time) . "\n";
echo "Processing rate: " . sprintf("%.0f", $total_processed / $total_time) . " organizations/second\n";
echo "Input file size: " . formatBytes($input_size) . "\n";
echo "Output file size: " . formatBytes($output_size) . "\n";
echo "Size reduction: " . sprintf("%.1f%%", $compression_ratio) . "\n";
echo "Output file: $output_file\n";

/**
 * Build a cache of available logo filenames for fast lookups
 */
function buildLogoCache($logos_dir) {
  $cache = array();
  
  // Get all SVG files
  $logo_files = glob($logos_dir . '*.svg');
  
  foreach ($logo_files as $logo_path) {
    $filename = basename($logo_path);
    $name_without_ext = pathinfo($filename, PATHINFO_FILENAME);
    
    // Store both with and without extension for flexible matching
    $cache[$name_without_ext] = $filename;
    $cache[$filename] = $filename;
  }
  
  return $cache;
}

/**
 * Find logo filename for organization using fast cache lookup
 */
function findLogoFilename($organization_name, $logo_cache) {
  if (empty($organization_name)) {
    return null;
  }
  
  // Normalize the organization name
  $normalized_name = normalizeNameForLogo($organization_name);
  
  // Try exact match first
  if (isset($logo_cache[$normalized_name])) {
    return $logo_cache[$normalized_name];
  }
  
  // Try variations
  $variations = getNameVariations($normalized_name);
  
  foreach ($variations as $variation) {
    if (isset($logo_cache[$variation])) {
      return $logo_cache[$variation];
    }
  }
  
  return null;
}

/**
 * Normalize organization name for logo matching
 */
function normalizeNameForLogo($name) {
  // Convert to lowercase
  $name = strtolower($name);
  
  // Remove common business suffixes
  $suffixes = array(
    ' inc', ' inc.', ' incorporated',
    ' ltd', ' ltd.', ' limited',
    ' llc', ' l.l.c.', ' corp', ' corp.',
    ' corporation', ' company', ' co',
    ' co.', ' group', ' holdings',
    ' international', ' intl', ' global',
    ' worldwide', ' enterprises', ' solutions',
    ' services', ' systems', ' technologies',
    ' tech', ' software', ' consulting'
  );
  
  foreach ($suffixes as $suffix) {
    if (substr($name, -strlen($suffix)) === $suffix) {
      $name = substr($name, 0, -strlen($suffix));
      break;
    }
  }
  
  // Remove special characters and replace with allowed characters
  $name = preg_replace('/[^a-z0-9\s\-_&]/', '', $name);
  
  // Replace spaces and multiple separators
  $name = preg_replace('/[\s\-_]+/', '', $name);
  
  // Handle special cases
  $replacements = array(
    '&' => 'and',
    'at&t' => 'atandt',
    'h&m' => 'handm',
    'p&g' => 'pandg',
    'r&d' => 'randd'
  );
  
  foreach ($replacements as $search => $replace) {
    $name = str_replace($search, $replace, $name);
  }
  
  return trim($name);
}

/**
 * Get variations of a name to try for logo matching
 */
function getNameVariations($name) {
  $variations = array();
  
  // Original name
  $variations[] = $name;
  
  // Try with dots for abbreviations
  if (strlen($name) <= 5 && ctype_alpha($name)) {
    $variations[] = implode('.', str_split($name));
    $variations[] = implode('dot', str_split($name));
  }
  
  // Try common abbreviations
  $abbreviations = array(
    'microsoft' => 'ms',
    'international' => 'intl',
    'corporation' => 'corp',
    'company' => 'co',
    'limited' => 'ltd',
    'incorporated' => 'inc',
    'technologies' => 'tech'
  );
  
  foreach ($abbreviations as $full => $abbr) {
    if (strpos($name, $full) !== false) {
      $variations[] = str_replace($full, $abbr, $name);
    }
    if (strpos($name, $abbr) !== false) {
      $variations[] = str_replace($abbr, $full, $name);
    }
  }
  
  // Try first word only for multi-word names
  if (strpos($name, ' ') !== false) {
    $first_word = explode(' ', $name)[0];
    if (strlen($first_word) > 2) {
      $variations[] = $first_word;
    }
  }
  
  // Remove duplicates
  return array_unique($variations);
}

/**
 * Format bytes into human readable format
 */
function formatBytes($bytes, $precision = 2) {
  $units = array('B', 'KB', 'MB', 'GB', 'TB');
  
  for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
    $bytes /= 1024;
  }
  
  return round($bytes, $precision) . ' ' . $units[$i];
}

/**
 * Format time into human readable format
 */
function formatTime($seconds) {
  if ($seconds < 60) {
    return sprintf("%.1fs", $seconds);
  } elseif ($seconds < 3600) {
    return sprintf("%dm %.1fs", floor($seconds / 60), $seconds % 60);
  } else {
    return sprintf("%dh %dm %.1fs", floor($seconds / 3600), floor(($seconds % 3600) / 60), $seconds % 60);
  }
}

echo "\nDone! You can now use the filtered dataset: company-dataset-filtered.json\n";
?>

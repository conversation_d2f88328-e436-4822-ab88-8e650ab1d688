<?php
/**
 * Convert company dataset JSON to SQLite database
 * 
 * This script converts the large company-dataset.json file into a SQLite database
 * and matches logo filenames for each company.
 * 
 * Usage: php convert-dataset.php
 */

// Set memory limit and execution time for large dataset
ini_set('memory_limit', '2G');
set_time_limit(0);

// Define paths
$plugin_dir = dirname(__DIR__);
$json_file = $plugin_dir . '/company-dataset.json';
$db_file = $plugin_dir . '/database/organizations.db';
$logos_dir = $plugin_dir . '/logos/';

// Ensure database directory exists
$db_dir = dirname($db_file);
if (!file_exists($db_dir)) {
  mkdir($db_dir, 0755, true);
}

// Include utility functions
require_once $plugin_dir . '/includes/class-utils.php';

/**
 * Normalize organization name for logo matching
 */
function normalize_name_for_logo($name) {
  // Convert to lowercase
  $name = strtolower($name);
  
  // Remove common business suffixes
  $suffixes = array(
    ' inc', ' inc.', ' incorporated',
    ' ltd', ' ltd.', ' limited',
    ' llc', ' l.l.c.', ' corp', ' corp.',
    ' corporation', ' company', ' co',
    ' co.', ' group', ' holdings',
    ' international', ' intl', ' global',
    ' worldwide', ' enterprises', ' solutions',
    ' services', ' systems', ' technologies',
    ' tech', ' software', ' consulting'
  );
  
  foreach ($suffixes as $suffix) {
    if (substr($name, -strlen($suffix)) === $suffix) {
      $name = substr($name, 0, -strlen($suffix));
      break;
    }
  }
  
  // Remove special characters and replace with allowed characters
  $name = preg_replace('/[^a-z0-9\s\-_&]/', '', $name);
  
  // Replace spaces and multiple separators with single separator
  $name = preg_replace('/[\s\-_]+/', '', $name);
  
  // Handle special cases for common replacements
  $replacements = array(
    '&' => 'and',
    'at&t' => 'atandt',
    'h&m' => 'handm',
    'p&g' => 'pandg',
    'r&d' => 'randd'
  );
  
  foreach ($replacements as $search => $replace) {
    $name = str_replace($search, $replace, $name);
  }
  
  return trim($name);
}

/**
 * Get name variations to try for logo matching
 */
function get_name_variations($name) {
  $variations = array();
  
  // Original normalized name
  $variations[] = $name;
  
  // Try with dots for abbreviations
  if (strlen($name) <= 5 && ctype_alpha($name)) {
    $variations[] = implode('.', str_split($name));
    $variations[] = implode('dot', str_split($name));
  }
  
  // Try common abbreviations and expansions
  $abbreviations = array(
    'microsoft' => 'ms',
    'international' => 'intl',
    'corporation' => 'corp',
    'company' => 'co',
    'limited' => 'ltd',
    'incorporated' => 'inc',
    'technologies' => 'tech',
    'systems' => 'sys',
    'solutions' => 'sol'
  );
  
  foreach ($abbreviations as $full => $abbr) {
    if (strpos($name, $full) !== false) {
      $variations[] = str_replace($full, $abbr, $name);
    }
    if (strpos($name, $abbr) !== false) {
      $variations[] = str_replace($abbr, $full, $name);
    }
  }
  
  // Try without common words
  $common_words = array('the', 'a', 'an', 'of', 'for', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'by');
  $words = explode(' ', $name);
  $filtered_words = array_diff($words, $common_words);
  if (count($filtered_words) !== count($words)) {
    $variations[] = implode('', $filtered_words);
  }
  
  // Try first word only for multi-word names
  if (strpos($name, ' ') !== false) {
    $first_word = explode(' ', $name)[0];
    if (strlen($first_word) > 2) {
      $variations[] = $first_word;
    }
  }
  
  // Remove duplicates and empty values
  $variations = array_unique(array_filter($variations));
  
  return $variations;
}

/**
 * Find logo filename for organization
 */
function find_logo_filename($organization_name, $logos_dir) {
  if (empty($organization_name)) {
    return null;
  }
  
  // Normalize the organization name for logo matching
  $normalized_name = normalize_name_for_logo($organization_name);
  
  // Try different variations of the name
  $variations = get_name_variations($normalized_name);
  
  foreach ($variations as $variation) {
    $logo_path = $logos_dir . $variation . '.svg';
    if (file_exists($logo_path)) {
      return $variation . '.svg';
    }
  }
  
  return null;
}

/**
 * Create database and table
 */
function create_database($db_file) {
  try {
    $pdo = new PDO('sqlite:' . $db_file);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Create organizations table
    $sql = "
      CREATE TABLE IF NOT EXISTS organizations (
        id TEXT PRIMARY KEY,
        website TEXT,
        name TEXT NOT NULL,
        founded INTEGER,
        size TEXT,
        locality TEXT,
        region TEXT,
        country TEXT,
        industry TEXT,
        linkedin_url TEXT,
        logo_filename TEXT,
        search_index TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    ";
    
    $pdo->exec($sql);
    
    // Create indexes for better search performance
    $indexes = array(
      "CREATE INDEX IF NOT EXISTS idx_name ON organizations(name)",
      "CREATE INDEX IF NOT EXISTS idx_website ON organizations(website)",
      "CREATE INDEX IF NOT EXISTS idx_industry ON organizations(industry)",
      "CREATE INDEX IF NOT EXISTS idx_country ON organizations(country)",
      "CREATE INDEX IF NOT EXISTS idx_size ON organizations(size)",
      "CREATE INDEX IF NOT EXISTS idx_search ON organizations(search_index)"
    );
    
    foreach ($indexes as $index_sql) {
      $pdo->exec($index_sql);
    }
    
    return $pdo;
  } catch (PDOException $e) {
    die("Database creation failed: " . $e->getMessage() . "\n");
  }
}

/**
 * Build search index for organization
 */
function build_search_index($organization) {
  $searchable_fields = array(
    'name', 'website', 'industry', 'locality', 
    'region', 'country', 'linkedin_url'
  );
  
  $search_terms = array();
  foreach ($searchable_fields as $field) {
    if (!empty($organization[$field])) {
      $search_terms[] = strtolower($organization[$field]);
    }
  }
  
  return implode(' ', $search_terms);
}

// Main execution
echo "Starting dataset conversion...\n";

// Check if JSON file exists
if (!file_exists($json_file)) {
  die("Error: company-dataset.json not found in plugin directory.\n");
}

// Check if logos directory exists
if (!file_exists($logos_dir)) {
  die("Error: logos directory not found.\n");
}

// Create database
echo "Creating database...\n";
$pdo = create_database($db_file);

// Prepare insert statement
$insert_sql = "
  INSERT OR REPLACE INTO organizations 
  (id, website, name, founded, size, locality, region, country, industry, linkedin_url, logo_filename, search_index) 
  VALUES 
  (:id, :website, :name, :founded, :size, :locality, :region, :country, :industry, :linkedin_url, :logo_filename, :search_index)
";

$stmt = $pdo->prepare($insert_sql);

// Process JSON file line by line
echo "Processing organizations...\n";
$handle = fopen($json_file, 'r');
$count = 0;
$logo_matches = 0;

// Start transaction for better performance
$pdo->beginTransaction();

while (($line = fgets($handle)) !== false) {
  $organization = json_decode(trim($line), true);
  
  if (!$organization || !isset($organization['name'])) {
    continue;
  }
  
  // Find logo filename
  $logo_filename = find_logo_filename($organization['name'], $logos_dir);
  if ($logo_filename) {
    $logo_matches++;
  }
  
  // Build search index
  $search_index = build_search_index($organization);
  
  // Insert into database
  $stmt->execute(array(
    ':id' => $organization['id'] ?? null,
    ':website' => $organization['website'] ?? null,
    ':name' => $organization['name'],
    ':founded' => !empty($organization['founded']) ? intval($organization['founded']) : null,
    ':size' => $organization['size'] ?? null,
    ':locality' => $organization['locality'] ?? null,
    ':region' => $organization['region'] ?? null,
    ':country' => $organization['country'] ?? null,
    ':industry' => $organization['industry'] ?? null,
    ':linkedin_url' => $organization['linkedin_url'] ?? null,
    ':logo_filename' => $logo_filename,
    ':search_index' => $search_index
  ));
  
  $count++;
  
  // Show progress every 10000 records
  if ($count % 10000 === 0) {
    echo "Processed $count organizations...\n";
  }
  
  // Commit transaction every 50000 records to avoid memory issues
  if ($count % 50000 === 0) {
    $pdo->commit();
    $pdo->beginTransaction();
  }
}

// Commit final transaction
$pdo->commit();

fclose($handle);

echo "\nConversion completed!\n";
echo "Total organizations processed: $count\n";
echo "Logo matches found: $logo_matches\n";
echo "Database saved to: $db_file\n";

// Show some statistics
try {
  $stats_queries = array(
    "Total organizations" => "SELECT COUNT(*) as count FROM organizations",
    "Organizations with logos" => "SELECT COUNT(*) as count FROM organizations WHERE logo_filename IS NOT NULL",
    "Organizations with websites" => "SELECT COUNT(*) as count FROM organizations WHERE website IS NOT NULL",
    "Organizations with LinkedIn" => "SELECT COUNT(*) as count FROM organizations WHERE linkedin_url IS NOT NULL",
    "Top 5 countries" => "SELECT country, COUNT(*) as count FROM organizations WHERE country IS NOT NULL GROUP BY country ORDER BY count DESC LIMIT 5",
    "Top 5 industries" => "SELECT industry, COUNT(*) as count FROM organizations WHERE industry IS NOT NULL GROUP BY industry ORDER BY count DESC LIMIT 5"
  );
  
  echo "\n--- Database Statistics ---\n";
  foreach ($stats_queries as $label => $query) {
    $result = $pdo->query($query);
    if (strpos($query, 'GROUP BY') !== false) {
      echo "$label:\n";
      while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
        echo "  " . ucwords($row[array_keys($row)[0]]) . ": " . $row['count'] . "\n";
      }
    } else {
      $row = $result->fetch(PDO::FETCH_ASSOC);
      echo "$label: " . $row['count'] . "\n";
    }
  }
} catch (PDOException $e) {
  echo "Error getting statistics: " . $e->getMessage() . "\n";
}

echo "\nDone!\n";
?>

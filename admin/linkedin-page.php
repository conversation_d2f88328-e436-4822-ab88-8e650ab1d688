<?php
/**
 * Company Data Enrichment Page
 * Uses free APIs: Clearbit Logo API, Company data enrichment
 */

// Prevent direct access
if (!defined('ABSPATH')) {
  exit;
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
  if (isset($_POST['enrich_organizations']) && wp_verify_nonce($_POST['_wpnonce'], 'enrich_organizations')) {
    $batch_size = intval($_POST['batch_size']);
    $batch_size = max(1, min(20, $batch_size)); // Limit between 1-20 for free APIs

    $enriched_count = enrich_organizations_batch($batch_size);

    if ($enriched_count > 0) {
      echo '<div class="notice notice-success"><p>' .
           sprintf(__('Successfully enriched %d organizations with company data!', 'baum-organizations'), $enriched_count) .
           '</p></div>';
    } else {
      echo '<div class="notice notice-warning"><p>' . __('No organizations were enriched. They may already have enrichment data or API limits reached.', 'baum-organizations') . '</p></div>';
    }
  }

  if (isset($_POST['clear_enrichment_data']) && wp_verify_nonce($_POST['_wpnonce'], 'clear_enrichment_data')) {
    $cleared_count = clear_enrichment_data();
    echo '<div class="notice notice-success"><p>' .
         sprintf(__('Cleared enrichment data from %d organizations.', 'baum-organizations'), $cleared_count) .
         '</p></div>';
  }

  if (isset($_POST['download_logos']) && wp_verify_nonce($_POST['_wpnonce'], 'download_logos')) {
    $batch_size = intval($_POST['logo_batch_size']);
    $batch_size = max(1, min(10, $batch_size)); // Smaller batches for logo downloads

    $downloaded_count = download_logos_batch($batch_size);

    if ($downloaded_count > 0) {
      echo '<div class="notice notice-success"><p>' .
           sprintf(__('Successfully downloaded %d company logos!', 'baum-organizations'), $downloaded_count) .
           '</p></div>';
    } else {
      echo '<div class="notice notice-warning"><p>' . __('No logos were downloaded. Companies may already have logos or no websites found.', 'baum-organizations') . '</p></div>';
    }
  }
}

// Get statistics
$total_orgs = BaumOrganizations::get_organization_count();
$enriched_orgs = get_enriched_organizations_count();
$pending_orgs = $total_orgs - $enriched_orgs;
$orgs_with_logos = get_organizations_with_logos_count();

/**
 * Enrich organizations with free company data
 */
function enrich_organizations_batch($batch_size = 10) {
  global $wpdb;
  $table_name = $wpdb->prefix . 'baum_organizations';

  // Get organizations without enrichment data
  $organizations = $wpdb->get_results($wpdb->prepare(
    "SELECT * FROM $table_name
     WHERE (linkedin_data IS NULL OR linkedin_data = '')
     AND website IS NOT NULL
     ORDER BY name ASC
     LIMIT %d",
    $batch_size
  ), ARRAY_A);

  $enriched_count = 0;

  foreach ($organizations as $org) {
    $enrichment_data = fetch_company_enrichment_data($org);

    if ($enrichment_data) {
      $wpdb->update(
        $table_name,
        array(
          'linkedin_data' => json_encode($enrichment_data),
          'enriched_at' => current_time('mysql')
        ),
        array('id' => $org['id'])
      );
      $enriched_count++;
    }

    // Add delay to respect API limits (be nice to free APIs)
    sleep(2);
  }

  return $enriched_count;
}

/**
 * Fetch company enrichment data using free APIs
 */
function fetch_company_enrichment_data($org) {
  if (empty($org['website'])) {
    return false;
  }

  $website = $org['website'];
  $enrichment_data = array(
    'company_name' => $org['name'],
    'website' => $website,
    'enrichment_source' => 'free_apis',
    'enriched_date' => current_time('mysql')
  );

  // 1. Try to get company data from Clearbit's free Logo API
  $logo_data = fetch_clearbit_logo_data($website);
  if ($logo_data) {
    $enrichment_data['logo_url'] = $logo_data['logo'];
    $enrichment_data['domain'] = $logo_data['domain'];
  }

  // 2. Generate enhanced description based on existing data
  $description_parts = array();
  if (!empty($org['industry'])) {
    $description_parts[] = ucwords($org['industry']) . ' company';
  }
  if (!empty($org['locality']) && !empty($org['country'])) {
    $description_parts[] = 'based in ' . ucwords($org['locality']) . ', ' . ucwords($org['country']);
  }
  if (!empty($org['founded'])) {
    $description_parts[] = 'founded in ' . $org['founded'];
  }
  if (!empty($org['size'])) {
    $description_parts[] = 'with ' . format_company_size_description($org['size']) . ' employees';
  }

  if (!empty($description_parts)) {
    $enrichment_data['description'] = ucwords($org['name']) . ' is a ' . implode(', ', $description_parts) . '.';
  }

  // 3. Add social media predictions
  $enrichment_data['social_media'] = generate_social_media_urls($org['name'], $website);

  // 4. Add industry insights
  if (!empty($org['industry'])) {
    $enrichment_data['industry_insights'] = get_industry_insights($org['industry']);
  }

  // 5. Add company size insights
  if (!empty($org['size'])) {
    $enrichment_data['size_category'] = categorize_company_size($org['size']);
  }

  return $enrichment_data;
}

/**
 * Fetch logo from Clearbit's free Logo API
 */
function fetch_clearbit_logo_data($website) {
  $domain = parse_url('https://' . $website, PHP_URL_HOST);
  if (!$domain) {
    return false;
  }

  $logo_url = 'https://logo.clearbit.com/' . $domain;

  // Check if logo exists by making a HEAD request
  $response = wp_remote_head($logo_url, array(
    'timeout' => 10,
    'user-agent' => 'Baum Organizations Plugin'
  ));

  if (!is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200) {
    return array(
      'logo' => $logo_url,
      'domain' => $domain
    );
  }

  return false;
}

/**
 * Generate social media URLs based on company name and website
 */
function generate_social_media_urls($company_name, $website) {
  $clean_name = strtolower(str_replace(array(' ', '.', ',', '&', 'inc', 'llc', 'ltd', 'corp'), '', $company_name));
  $domain = parse_url('https://' . $website, PHP_URL_HOST);
  $domain_name = str_replace('www.', '', $domain);
  $domain_base = explode('.', $domain_name)[0];

  return array(
    'linkedin' => 'https://linkedin.com/company/' . $clean_name,
    'twitter' => 'https://twitter.com/' . $domain_base,
    'facebook' => 'https://facebook.com/' . $clean_name,
    'instagram' => 'https://instagram.com/' . $clean_name
  );
}

/**
 * Get industry insights
 */
function get_industry_insights($industry) {
  $insights = array(
    'technology' => array('growth' => 'High', 'trend' => 'AI/ML adoption'),
    'software' => array('growth' => 'High', 'trend' => 'Cloud-first'),
    'healthcare' => array('growth' => 'Stable', 'trend' => 'Digital health'),
    'finance' => array('growth' => 'Stable', 'trend' => 'Fintech disruption'),
    'retail' => array('growth' => 'Moderate', 'trend' => 'E-commerce shift'),
    'manufacturing' => array('growth' => 'Stable', 'trend' => 'Industry 4.0'),
    'education' => array('growth' => 'Moderate', 'trend' => 'Online learning'),
    'consulting' => array('growth' => 'High', 'trend' => 'Digital transformation')
  );

  $industry_key = strtolower($industry);
  foreach ($insights as $key => $data) {
    if (strpos($industry_key, $key) !== false) {
      return $data;
    }
  }

  return array('growth' => 'Unknown', 'trend' => 'Industry evolution');
}

/**
 * Categorize company size
 */
function categorize_company_size($size) {
  if (strpos($size, '1-10') !== false) return 'Startup';
  if (strpos($size, '11-50') !== false) return 'Small Business';
  if (strpos($size, '51-200') !== false) return 'Medium Business';
  if (strpos($size, '201-500') !== false) return 'Large Business';
  if (strpos($size, '501-1000') !== false) return 'Large Enterprise';
  if (strpos($size, '1001-5000') !== false) return 'Enterprise';
  if (strpos($size, '5001-10000') !== false) return 'Large Enterprise';
  if (strpos($size, '10001+') !== false) return 'Fortune 500';

  return 'Unknown';
}

/**
 * Format company size for description
 */
function format_company_size_description($size) {
  if (strpos($size, '1-10') !== false) return '1-10';
  if (strpos($size, '11-50') !== false) return '11-50';
  if (strpos($size, '51-200') !== false) return '51-200';
  if (strpos($size, '201-500') !== false) return '201-500';
  if (strpos($size, '501-1000') !== false) return '501-1,000';
  if (strpos($size, '1001-5000') !== false) return '1,001-5,000';
  if (strpos($size, '5001-10000') !== false) return '5,001-10,000';
  if (strpos($size, '10001+') !== false) return 'over 10,000';

  return $size;
}

/**
 * Download company logos from Clearbit
 */
function download_logos_batch($batch_size = 10) {
  global $wpdb;
  $table_name = $wpdb->prefix . 'baum_organizations';

  // Get organizations with websites but no logos
  $organizations = $wpdb->get_results($wpdb->prepare(
    "SELECT * FROM $table_name
     WHERE website IS NOT NULL
     AND (logo_filename IS NULL OR logo_filename = '')
     ORDER BY name ASC
     LIMIT %d",
    $batch_size
  ), ARRAY_A);

  $downloaded_count = 0;

  foreach ($organizations as $org) {
    $logo_filename = download_company_logo($org);

    if ($logo_filename) {
      $wpdb->update(
        $table_name,
        array('logo_filename' => $logo_filename),
        array('id' => $org['id'])
      );
      $downloaded_count++;
    }

    // Add delay to be nice to Clearbit's free API
    sleep(1);
  }

  return $downloaded_count;
}

/**
 * Download a single company logo
 */
function download_company_logo($org) {
  if (empty($org['website'])) {
    return false;
  }

  $domain = parse_url('https://' . $org['website'], PHP_URL_HOST);
  if (!$domain) {
    return false;
  }

  $logo_url = 'https://logo.clearbit.com/' . $domain;

  // Download the logo
  $response = wp_remote_get($logo_url, array(
    'timeout' => 15,
    'user-agent' => 'Baum Organizations Plugin'
  ));

  if (is_wp_error($response) || wp_remote_retrieve_response_code($response) !== 200) {
    return false;
  }

  $image_data = wp_remote_retrieve_body($response);
  if (empty($image_data)) {
    return false;
  }

  // Create filename
  $clean_name = sanitize_file_name(strtolower(str_replace(' ', '-', $org['name'])));
  $filename = $clean_name . '.png';
  $file_path = BAUM_ORGS_LOGOS_DIR . $filename;

  // Save the file
  if (file_put_contents($file_path, $image_data)) {
    return $filename;
  }

  return false;
}

/**
 * Get count of enriched organizations
 */
function get_enriched_organizations_count() {
  global $wpdb;
  $table_name = $wpdb->prefix . 'baum_organizations';

  return $wpdb->get_var(
    "SELECT COUNT(*) FROM $table_name WHERE linkedin_data IS NOT NULL AND linkedin_data != ''"
  );
}

/**
 * Get count of organizations with logos
 */
function get_organizations_with_logos_count() {
  global $wpdb;
  $table_name = $wpdb->prefix . 'baum_organizations';

  return $wpdb->get_var(
    "SELECT COUNT(*) FROM $table_name WHERE logo_filename IS NOT NULL AND logo_filename != ''"
  );
}

/**
 * Clear enrichment data
 */
function clear_enrichment_data() {
  global $wpdb;
  $table_name = $wpdb->prefix . 'baum_organizations';

  return $wpdb->update(
    $table_name,
    array(
      'linkedin_data' => null,
      'enriched_at' => null
    ),
    array('1' => '1') // Update all rows
  );
}
?>

<div class="wrap">
  <h1><?php _e('Company Data Enrichment', 'baum-organizations'); ?></h1>

  <div class="notice notice-info">
    <p>
      <strong><?php _e('Free Company Data Enrichment', 'baum-organizations'); ?></strong><br>
      <?php _e('Enhance your organization data using free APIs including Clearbit logos, company descriptions, social media links, and industry insights.', 'baum-organizations'); ?>
    </p>
  </div>
  
  <!-- Statistics -->
  <div class="card">
    <h2><?php _e('Enrichment Statistics', 'baum-organizations'); ?></h2>
    
    <table class="form-table">
      <tr>
        <th scope="row"><?php _e('Total Organizations', 'baum-organizations'); ?></th>
        <td><strong><?php echo number_format($total_orgs); ?></strong></td>
      </tr>
      <tr>
        <th scope="row"><?php _e('Enriched with Company Data', 'baum-organizations'); ?></th>
        <td>
          <strong><?php echo number_format($enriched_orgs); ?></strong>
          <?php if ($total_orgs > 0): ?>
            <span class="description">(<?php echo round(($enriched_orgs / $total_orgs) * 100, 1); ?>%)</span>
          <?php endif; ?>
        </td>
      </tr>
      <tr>
        <th scope="row"><?php _e('Organizations with Logos', 'baum-organizations'); ?></th>
        <td>
          <strong><?php echo number_format($orgs_with_logos); ?></strong>
          <?php if ($total_orgs > 0): ?>
            <span class="description">(<?php echo round(($orgs_with_logos / $total_orgs) * 100, 1); ?>%)</span>
          <?php endif; ?>
        </td>
      </tr>
      <tr>
        <th scope="row"><?php _e('Pending Enrichment', 'baum-organizations'); ?></th>
        <td><strong><?php echo number_format($pending_orgs); ?></strong></td>
      </tr>
    </table>
    
    <?php if ($total_orgs > 0): ?>
      <div class="progress-bar" style="background: #f0f0f0; height: 20px; border-radius: 10px; overflow: hidden; margin: 15px 0;">
        <div style="background: #0073aa; height: 100%; width: <?php echo ($enriched_orgs / $total_orgs) * 100; ?>%; transition: width 0.3s ease;"></div>
      </div>
    <?php endif; ?>
  </div>
  
  <!-- Free APIs Information -->
  <div class="card">
    <h2><?php _e('Free Enrichment Sources', 'baum-organizations'); ?></h2>

    <p><?php _e('This enrichment system uses completely free APIs and services:', 'baum-organizations'); ?></p>

    <ul style="list-style: disc; margin-left: 20px;">
      <li><strong>Clearbit Logo API</strong> - Free company logos (no API key required)</li>
      <li><strong>Enhanced Descriptions</strong> - Generated from existing company data</li>
      <li><strong>Social Media Predictions</strong> - Intelligent URL generation</li>
      <li><strong>Industry Insights</strong> - Built-in industry knowledge base</li>
      <li><strong>Company Categorization</strong> - Size-based business categories</li>
    </ul>

    <div class="notice notice-success inline">
      <p>
        <strong><?php _e('No API Keys Required!', 'baum-organizations'); ?></strong>
        <?php _e('All enrichment features work out of the box without any configuration.', 'baum-organizations'); ?>
      </p>
    </div>
  </div>
  
  <!-- Enrichment Actions -->
  <div class="card">
    <h2><?php _e('Data Enrichment', 'baum-organizations'); ?></h2>

    <form method="post" style="margin-bottom: 30px;">
      <?php wp_nonce_field('enrich_organizations'); ?>

      <h3><?php _e('Enrich Company Data', 'baum-organizations'); ?></h3>
      <p><?php _e('Add descriptions, social media links, and industry insights to organizations.', 'baum-organizations'); ?></p>

      <table class="form-table">
        <tr>
          <th scope="row">
            <label for="batch_size"><?php _e('Batch Size', 'baum-organizations'); ?></label>
          </th>
          <td>
            <input type="number" id="batch_size" name="batch_size" value="10"
                   min="1" max="20" class="small-text">
            <p class="description">
              <?php _e('Number of organizations to enrich in this batch (1-20). Smaller batches are more reliable.', 'baum-organizations'); ?>
            </p>
          </td>
        </tr>
      </table>

      <p class="submit">
        <input type="submit" name="enrich_organizations" class="button-primary"
               value="<?php _e('Enrich Company Data', 'baum-organizations'); ?>"
               onclick="return confirm('<?php _e('This will enrich organizations with company data. Continue?', 'baum-organizations'); ?>')">
      </p>
    </form>

    <hr>

    <form method="post" style="margin-bottom: 20px;">
      <?php wp_nonce_field('download_logos'); ?>

      <h3><?php _e('Download Company Logos', 'baum-organizations'); ?></h3>
      <p><?php _e('Download high-quality logos from Clearbit for organizations with websites.', 'baum-organizations'); ?></p>

      <table class="form-table">
        <tr>
          <th scope="row">
            <label for="logo_batch_size"><?php _e('Batch Size', 'baum-organizations'); ?></label>
          </th>
          <td>
            <input type="number" id="logo_batch_size" name="logo_batch_size" value="5"
                   min="1" max="10" class="small-text">
            <p class="description">
              <?php _e('Number of logos to download in this batch (1-10). Be respectful to free APIs.', 'baum-organizations'); ?>
            </p>
          </td>
        </tr>
      </table>

      <p class="submit">
        <input type="submit" name="download_logos" class="button-secondary"
               value="<?php _e('Download Logos', 'baum-organizations'); ?>"
               onclick="return confirm('<?php _e('This will download company logos. Continue?', 'baum-organizations'); ?>')">
      </p>
    </form>
    
    <!-- Clear Data -->
    <hr>

    <form method="post">
      <?php wp_nonce_field('clear_enrichment_data'); ?>

      <h3><?php _e('Clear Enrichment Data', 'baum-organizations'); ?></h3>
      <p><?php _e('Remove all enrichment data from organizations (keeps original company data).', 'baum-organizations'); ?></p>

      <p class="submit">
        <input type="submit" name="clear_enrichment_data" class="button button-secondary"
               value="<?php _e('Clear All Enrichment Data', 'baum-organizations'); ?>"
               onclick="return confirm('<?php _e('This will remove all enrichment data. Are you sure?', 'baum-organizations'); ?>')">
      </p>
    </form>
  </div>
  
  <!-- Recent Enrichments -->
  <?php if ($enriched_orgs > 0): ?>
    <div class="card">
      <h2><?php _e('Recently Enriched Organizations', 'baum-organizations'); ?></h2>

      <?php
      global $wpdb;
      $table_name = $wpdb->prefix . 'baum_organizations';
      $recent = $wpdb->get_results(
        "SELECT name, website, logo_filename, enriched_at FROM $table_name
         WHERE linkedin_data IS NOT NULL
         ORDER BY enriched_at DESC
         LIMIT 10",
        ARRAY_A
      );
      ?>

      <table class="wp-list-table widefat fixed striped">
        <thead>
          <tr>
            <th style="width: 60px;"><?php _e('Logo', 'baum-organizations'); ?></th>
            <th><?php _e('Organization', 'baum-organizations'); ?></th>
            <th><?php _e('Website', 'baum-organizations'); ?></th>
            <th><?php _e('Enriched Date', 'baum-organizations'); ?></th>
          </tr>
        </thead>
        <tbody>
          <?php foreach ($recent as $org): ?>
            <tr>
              <td>
                <?php if (!empty($org['logo_filename'])): ?>
                  <img src="<?php echo esc_url(BAUM_ORGS_LOGOS_URL . $org['logo_filename']); ?>"
                       alt="<?php echo esc_attr($org['name']); ?>"
                       style="width: 40px; height: 40px; object-fit: contain; border-radius: 4px; background: #f0f0f0;">
                <?php else: ?>
                  <div style="width: 40px; height: 40px; background: #f0f0f0; border-radius: 4px; display: flex; align-items: center; justify-content: center;">
                    <span style="color: #999;">🏢</span>
                  </div>
                <?php endif; ?>
              </td>
              <td><strong><?php echo esc_html($org['name']); ?></strong></td>
              <td>
                <?php if ($org['website']): ?>
                  <a href="https://<?php echo esc_attr($org['website']); ?>" target="_blank">
                    <?php echo esc_html($org['website']); ?>
                  </a>
                <?php endif; ?>
              </td>
              <td><?php echo esc_html(date('M j, Y g:i A', strtotime($org['enriched_at']))); ?></td>
            </tr>
          <?php endforeach; ?>
        </tbody>
      </table>

      <p class="description">
        <?php _e('Tip: View the full enrichment data by clicking on an organization in the main Organizations list.', 'baum-organizations'); ?>
      </p>
    </div>
  <?php endif; ?>
</div>

<style>
.card {
  background: #fff;
  border: 1px solid #ccd0d4;
  border-radius: 4px;
  padding: 20px;
  margin: 20px 0;
  box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.card h2 {
  margin-top: 0;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.notice.inline {
  margin: 15px 0;
  padding: 12px;
}

.progress-bar {
  position: relative;
}

.progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}
</style>

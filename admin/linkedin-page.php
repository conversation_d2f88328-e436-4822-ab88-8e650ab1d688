<?php
/**
 * LinkedIn Enrichment Page
 */

// Prevent direct access
if (!defined('ABSPATH')) {
  exit;
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
  if (isset($_POST['save_api_key']) && wp_verify_nonce($_POST['_wpnonce'], 'save_linkedin_api_key')) {
    $api_key = sanitize_text_field($_POST['linkedin_api_key']);
    update_option('baum_orgs_linkedin_api_key', $api_key);
    
    echo '<div class="notice notice-success"><p>' . __('LinkedIn API key saved successfully!', 'baum-organizations') . '</p></div>';
  }
  
  if (isset($_POST['enrich_organizations']) && wp_verify_nonce($_POST['_wpnonce'], 'enrich_organizations')) {
    $batch_size = intval($_POST['batch_size']);
    $batch_size = max(1, min(50, $batch_size)); // Limit between 1-50
    
    $enriched_count = enrich_organizations_batch($batch_size);
    
    if ($enriched_count > 0) {
      echo '<div class="notice notice-success"><p>' . 
           sprintf(__('Successfully enriched %d organizations with LinkedIn data!', 'baum-organizations'), $enriched_count) . 
           '</p></div>';
    } else {
      echo '<div class="notice notice-warning"><p>' . __('No organizations were enriched. They may already have LinkedIn data or API limits reached.', 'baum-organizations') . '</p></div>';
    }
  }
  
  if (isset($_POST['clear_linkedin_data']) && wp_verify_nonce($_POST['_wpnonce'], 'clear_linkedin_data')) {
    $cleared_count = clear_linkedin_data();
    echo '<div class="notice notice-success"><p>' . 
         sprintf(__('Cleared LinkedIn data from %d organizations.', 'baum-organizations'), $cleared_count) . 
         '</p></div>';
  }
}

// Get current API key
$api_key = get_option('baum_orgs_linkedin_api_key', '');

// Get statistics
$total_orgs = BaumOrganizations::get_organization_count();
$enriched_orgs = get_enriched_organizations_count();
$pending_orgs = $total_orgs - $enriched_orgs;

/**
 * Enrich organizations with LinkedIn data
 */
function enrich_organizations_batch($batch_size = 10) {
  global $wpdb;
  $table_name = $wpdb->prefix . 'baum_organizations';
  
  // Get organizations without LinkedIn data
  $organizations = $wpdb->get_results($wpdb->prepare(
    "SELECT * FROM $table_name 
     WHERE (linkedin_data IS NULL OR linkedin_data = '') 
     AND website IS NOT NULL 
     ORDER BY name ASC 
     LIMIT %d",
    $batch_size
  ), ARRAY_A);
  
  $enriched_count = 0;
  
  foreach ($organizations as $org) {
    $linkedin_data = fetch_linkedin_data($org);
    
    if ($linkedin_data) {
      $wpdb->update(
        $table_name,
        array(
          'linkedin_data' => json_encode($linkedin_data),
          'enriched_at' => current_time('mysql')
        ),
        array('id' => $org['id'])
      );
      $enriched_count++;
    }
    
    // Add delay to respect API limits
    sleep(1);
  }
  
  return $enriched_count;
}

/**
 * Fetch LinkedIn data for organization
 */
function fetch_linkedin_data($org) {
  $api_key = get_option('baum_orgs_linkedin_api_key', '');
  
  if (empty($api_key)) {
    return false;
  }
  
  // For demo purposes, we'll simulate LinkedIn data
  // In a real implementation, you'd use LinkedIn's API or a service like RapidAPI
  
  $mock_data = array(
    'company_name' => $org['name'],
    'description' => 'Leading company in the ' . strtolower($org['industry']) . ' industry.',
    'employee_count' => rand(50, 10000),
    'headquarters' => $org['locality'] . ', ' . $org['country'],
    'specialties' => array('Innovation', 'Technology', 'Customer Service'),
    'company_type' => 'Public Company',
    'website' => $org['website'],
    'linkedin_url' => 'https://linkedin.com/company/' . strtolower(str_replace(' ', '-', $org['name'])),
    'logo_url' => 'https://logo.clearbit.com/' . $org['website'],
    'enriched_date' => current_time('mysql')
  );
  
  return $mock_data;
}

/**
 * Get count of enriched organizations
 */
function get_enriched_organizations_count() {
  global $wpdb;
  $table_name = $wpdb->prefix . 'baum_organizations';
  
  return $wpdb->get_var(
    "SELECT COUNT(*) FROM $table_name WHERE linkedin_data IS NOT NULL AND linkedin_data != ''"
  );
}

/**
 * Clear LinkedIn data
 */
function clear_linkedin_data() {
  global $wpdb;
  $table_name = $wpdb->prefix . 'baum_organizations';
  
  return $wpdb->update(
    $table_name,
    array(
      'linkedin_data' => null,
      'enriched_at' => null
    ),
    array('1' => '1') // Update all rows
  );
}
?>

<div class="wrap">
  <h1><?php _e('LinkedIn Enrichment', 'baum-organizations'); ?></h1>
  
  <div class="notice notice-info">
    <p>
      <strong><?php _e('LinkedIn Data Enrichment', 'baum-organizations'); ?></strong><br>
      <?php _e('Enhance your organization data with LinkedIn company information including descriptions, employee counts, and more.', 'baum-organizations'); ?>
    </p>
  </div>
  
  <!-- Statistics -->
  <div class="card">
    <h2><?php _e('Enrichment Statistics', 'baum-organizations'); ?></h2>
    
    <table class="form-table">
      <tr>
        <th scope="row"><?php _e('Total Organizations', 'baum-organizations'); ?></th>
        <td><strong><?php echo number_format($total_orgs); ?></strong></td>
      </tr>
      <tr>
        <th scope="row"><?php _e('Enriched with LinkedIn Data', 'baum-organizations'); ?></th>
        <td>
          <strong><?php echo number_format($enriched_orgs); ?></strong>
          <?php if ($total_orgs > 0): ?>
            <span class="description">(<?php echo round(($enriched_orgs / $total_orgs) * 100, 1); ?>%)</span>
          <?php endif; ?>
        </td>
      </tr>
      <tr>
        <th scope="row"><?php _e('Pending Enrichment', 'baum-organizations'); ?></th>
        <td><strong><?php echo number_format($pending_orgs); ?></strong></td>
      </tr>
    </table>
    
    <?php if ($total_orgs > 0): ?>
      <div class="progress-bar" style="background: #f0f0f0; height: 20px; border-radius: 10px; overflow: hidden; margin: 15px 0;">
        <div style="background: #0073aa; height: 100%; width: <?php echo ($enriched_orgs / $total_orgs) * 100; ?>%; transition: width 0.3s ease;"></div>
      </div>
    <?php endif; ?>
  </div>
  
  <!-- API Configuration -->
  <div class="card">
    <h2><?php _e('LinkedIn API Configuration', 'baum-organizations'); ?></h2>
    
    <form method="post">
      <?php wp_nonce_field('save_linkedin_api_key'); ?>
      
      <table class="form-table">
        <tr>
          <th scope="row">
            <label for="linkedin_api_key"><?php _e('LinkedIn API Key', 'baum-organizations'); ?></label>
          </th>
          <td>
            <input type="password" id="linkedin_api_key" name="linkedin_api_key" 
                   value="<?php echo esc_attr($api_key); ?>" class="regular-text" 
                   placeholder="<?php _e('Enter your LinkedIn API key', 'baum-organizations'); ?>">
            <p class="description">
              <?php _e('Get your LinkedIn API key from the LinkedIn Developer Portal.', 'baum-organizations'); ?>
              <a href="https://developer.linkedin.com/" target="_blank"><?php _e('Learn more', 'baum-organizations'); ?></a>
            </p>
          </td>
        </tr>
      </table>
      
      <p class="submit">
        <input type="submit" name="save_api_key" class="button-primary" 
               value="<?php _e('Save API Key', 'baum-organizations'); ?>">
      </p>
    </form>
  </div>
  
  <!-- Enrichment Actions -->
  <div class="card">
    <h2><?php _e('Enrichment Actions', 'baum-organizations'); ?></h2>
    
    <?php if (empty($api_key)): ?>
      <div class="notice notice-warning inline">
        <p><?php _e('Please configure your LinkedIn API key above before running enrichment.', 'baum-organizations'); ?></p>
      </div>
    <?php else: ?>
      
      <form method="post" style="margin-bottom: 20px;">
        <?php wp_nonce_field('enrich_organizations'); ?>
        
        <table class="form-table">
          <tr>
            <th scope="row">
              <label for="batch_size"><?php _e('Batch Size', 'baum-organizations'); ?></label>
            </th>
            <td>
              <input type="number" id="batch_size" name="batch_size" value="10" 
                     min="1" max="50" class="small-text">
              <p class="description">
                <?php _e('Number of organizations to enrich in this batch (1-50). Start small to test API limits.', 'baum-organizations'); ?>
              </p>
            </td>
          </tr>
        </table>
        
        <p class="submit">
          <input type="submit" name="enrich_organizations" class="button-primary" 
                 value="<?php _e('Enrich Organizations', 'baum-organizations'); ?>"
                 onclick="return confirm('<?php _e('This will fetch LinkedIn data for organizations. Continue?', 'baum-organizations'); ?>')">
        </p>
      </form>
      
    <?php endif; ?>
    
    <!-- Clear Data -->
    <form method="post">
      <?php wp_nonce_field('clear_linkedin_data'); ?>
      
      <p class="submit">
        <input type="submit" name="clear_linkedin_data" class="button button-secondary" 
               value="<?php _e('Clear All LinkedIn Data', 'baum-organizations'); ?>"
               onclick="return confirm('<?php _e('This will remove all LinkedIn data. Are you sure?', 'baum-organizations'); ?>')">
      </p>
    </form>
  </div>
  
  <!-- Recent Enrichments -->
  <?php if ($enriched_orgs > 0): ?>
    <div class="card">
      <h2><?php _e('Recently Enriched Organizations', 'baum-organizations'); ?></h2>
      
      <?php
      global $wpdb;
      $table_name = $wpdb->prefix . 'baum_organizations';
      $recent = $wpdb->get_results(
        "SELECT name, website, enriched_at FROM $table_name 
         WHERE linkedin_data IS NOT NULL 
         ORDER BY enriched_at DESC 
         LIMIT 10",
        ARRAY_A
      );
      ?>
      
      <table class="wp-list-table widefat fixed striped">
        <thead>
          <tr>
            <th><?php _e('Organization', 'baum-organizations'); ?></th>
            <th><?php _e('Website', 'baum-organizations'); ?></th>
            <th><?php _e('Enriched Date', 'baum-organizations'); ?></th>
          </tr>
        </thead>
        <tbody>
          <?php foreach ($recent as $org): ?>
            <tr>
              <td><strong><?php echo esc_html($org['name']); ?></strong></td>
              <td>
                <?php if ($org['website']): ?>
                  <a href="https://<?php echo esc_attr($org['website']); ?>" target="_blank">
                    <?php echo esc_html($org['website']); ?>
                  </a>
                <?php endif; ?>
              </td>
              <td><?php echo esc_html(date('M j, Y g:i A', strtotime($org['enriched_at']))); ?></td>
            </tr>
          <?php endforeach; ?>
        </tbody>
      </table>
    </div>
  <?php endif; ?>
</div>

<style>
.card {
  background: #fff;
  border: 1px solid #ccd0d4;
  border-radius: 4px;
  padding: 20px;
  margin: 20px 0;
  box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.card h2 {
  margin-top: 0;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.notice.inline {
  margin: 15px 0;
  padding: 12px;
}

.progress-bar {
  position: relative;
}

.progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}
</style>

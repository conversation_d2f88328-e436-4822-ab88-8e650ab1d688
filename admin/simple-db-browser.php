<?php
/**
 * Simple Database Browser for Baum Organizations
 * Fallback when Adminer doesn't work
 */

// Prevent direct access
if (!defined('ABSPATH')) {
  exit;
}

// Handle AJAX requests
if (isset($_POST['action']) && $_POST['action'] === 'baum_db_query') {
  if (!wp_verify_nonce($_POST['nonce'], 'baum_db_query')) {
    wp_die('Security check failed');
  }
  
  $query = sanitize_textarea_field($_POST['query']);
  $db = BaumOrganizations::get_db_connection();
  
  if (!$db) {
    wp_die('Database connection failed');
  }
  
  try {
    $stmt = $db->prepare($query);
    $stmt->execute();
    
    if (stripos($query, 'SELECT') === 0) {
      $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
      
      if (!empty($results)) {
        echo '<table class="wp-list-table widefat fixed striped">';
        echo '<thead><tr>';
        foreach (array_keys($results[0]) as $column) {
          echo '<th>' . esc_html($column) . '</th>';
        }
        echo '</tr></thead><tbody>';
        
        foreach ($results as $row) {
          echo '<tr>';
          foreach ($row as $value) {
            $display_value = is_null($value) ? '<em>NULL</em>' : esc_html($value);
            echo '<td>' . $display_value . '</td>';
          }
          echo '</tr>';
        }
        echo '</tbody></table>';
        
        echo '<p><strong>Rows returned:</strong> ' . count($results) . '</p>';
      } else {
        echo '<p>No results found.</p>';
      }
    } else {
      $affected = $stmt->rowCount();
      echo '<div class="notice notice-success"><p>Query executed successfully. Affected rows: ' . $affected . '</p></div>';
    }
    
  } catch (PDOException $e) {
    echo '<div class="notice notice-error"><p>Error: ' . esc_html($e->getMessage()) . '</p></div>';
  }
  
  wp_die();
}

// Get database info
$db = BaumOrganizations::get_db_connection();
$tables = array();
$table_info = array();

if ($db) {
  try {
    $tables = $db->query("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")->fetchAll(PDO::FETCH_COLUMN);
    
    foreach ($tables as $table) {
      $count = $db->query("SELECT COUNT(*) FROM `$table`")->fetchColumn();
      $table_info[$table] = $count;
    }
  } catch (PDOException $e) {
    error_log('Database error: ' . $e->getMessage());
  }
}
?>

<div class="wrap">
  <h1><?php _e('Simple Database Browser', 'baum-organizations'); ?></h1>
  
  <div class="notice notice-info">
    <p>
      <strong><?php _e('Note:', 'baum-organizations'); ?></strong>
      <?php _e('This is a simple database browser. For advanced features, try fixing Adminer or use a desktop SQLite client.', 'baum-organizations'); ?>
    </p>
  </div>
  
  <!-- Database Overview -->
  <div class="card">
    <h2><?php _e('Database Overview', 'baum-organizations'); ?></h2>
    
    <?php if (!empty($tables)): ?>
      <table class="form-table">
        <tr>
          <th scope="row"><?php _e('Database File', 'baum-organizations'); ?></th>
          <td><code><?php echo esc_html(BAUM_ORGS_DB_PATH); ?></code></td>
        </tr>
        <tr>
          <th scope="row"><?php _e('File Size', 'baum-organizations'); ?></th>
          <td><?php echo size_format(filesize(BAUM_ORGS_DB_PATH)); ?></td>
        </tr>
        <tr>
          <th scope="row"><?php _e('Tables', 'baum-organizations'); ?></th>
          <td><?php echo count($tables); ?></td>
        </tr>
      </table>
      
      <h3><?php _e('Tables', 'baum-organizations'); ?></h3>
      <table class="wp-list-table widefat fixed striped">
        <thead>
          <tr>
            <th><?php _e('Table Name', 'baum-organizations'); ?></th>
            <th><?php _e('Row Count', 'baum-organizations'); ?></th>
            <th><?php _e('Actions', 'baum-organizations'); ?></th>
          </tr>
        </thead>
        <tbody>
          <?php foreach ($table_info as $table => $count): ?>
            <tr>
              <td><strong><?php echo esc_html($table); ?></strong></td>
              <td><?php echo number_format($count); ?></td>
              <td>
                <button class="button button-small" onclick="loadTableData('<?php echo esc_js($table); ?>')">
                  <?php _e('Browse', 'baum-organizations'); ?>
                </button>
                <button class="button button-small" onclick="showTableStructure('<?php echo esc_js($table); ?>')">
                  <?php _e('Structure', 'baum-organizations'); ?>
                </button>
              </td>
            </tr>
          <?php endforeach; ?>
        </tbody>
      </table>
    <?php else: ?>
      <div class="notice notice-error">
        <p><?php _e('No database tables found or unable to connect to database.', 'baum-organizations'); ?></p>
      </div>
    <?php endif; ?>
  </div>
  
  <!-- SQL Query Interface -->
  <div class="card">
    <h2><?php _e('SQL Query', 'baum-organizations'); ?></h2>
    
    <form id="sql-query-form">
      <?php wp_nonce_field('baum_db_query', 'nonce'); ?>
      <input type="hidden" name="action" value="baum_db_query">
      
      <p>
        <label for="sql-query"><?php _e('Enter SQL Query:', 'baum-organizations'); ?></label>
        <textarea id="sql-query" name="query" rows="5" cols="80" class="large-text code" placeholder="SELECT * FROM organizations LIMIT 10;"></textarea>
      </p>
      
      <p>
        <button type="submit" class="button button-primary"><?php _e('Execute Query', 'baum-organizations'); ?></button>
        <button type="button" class="button" onclick="clearQuery()"><?php _e('Clear', 'baum-organizations'); ?></button>
      </p>
      
      <div class="notice notice-warning inline">
        <p>
          <strong><?php _e('Warning:', 'baum-organizations'); ?></strong>
          <?php _e('Be careful with UPDATE, DELETE, and DROP queries. Always backup your database first.', 'baum-organizations'); ?>
        </p>
      </div>
    </form>
    
    <div id="query-results"></div>
  </div>
  
  <!-- Quick Queries -->
  <div class="card">
    <h2><?php _e('Quick Queries', 'baum-organizations'); ?></h2>
    
    <div class="button-group">
      <button class="button" onclick="runQuickQuery('SELECT COUNT(*) as total_organizations FROM organizations')">
        <?php _e('Total Organizations', 'baum-organizations'); ?>
      </button>
      
      <button class="button" onclick="runQuickQuery('SELECT industry, COUNT(*) as count FROM organizations WHERE industry IS NOT NULL GROUP BY industry ORDER BY count DESC LIMIT 10')">
        <?php _e('Top Industries', 'baum-organizations'); ?>
      </button>
      
      <button class="button" onclick="runQuickQuery('SELECT country, COUNT(*) as count FROM organizations WHERE country IS NOT NULL GROUP BY country ORDER BY count DESC LIMIT 10')">
        <?php _e('Top Countries', 'baum-organizations'); ?>
      </button>
      
      <button class="button" onclick="runQuickQuery('SELECT * FROM organizations WHERE logo_filename IS NOT NULL LIMIT 10')">
        <?php _e('Organizations with Logos', 'baum-organizations'); ?>
      </button>
      
      <button class="button" onclick="runQuickQuery('SELECT size, COUNT(*) as count FROM organizations WHERE size IS NOT NULL GROUP BY size ORDER BY count DESC')">
        <?php _e('Company Sizes', 'baum-organizations'); ?>
      </button>
    </div>
  </div>
  
  <p>
    <a href="<?php echo admin_url('admin.php?page=baum-organizations-db'); ?>" class="button">
      <?php _e('Back to Database Manager', 'baum-organizations'); ?>
    </a>
  </p>
</div>

<style>
.card {
  background: #fff;
  border: 1px solid #ccd0d4;
  border-radius: 4px;
  padding: 20px;
  margin: 20px 0;
  box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.card h2 {
  margin-top: 0;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.notice.inline {
  margin: 15px 0;
  padding: 12px;
}

.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.button-group .button {
  margin: 0;
}

#query-results {
  margin-top: 20px;
}

.code {
  font-family: Consolas, Monaco, monospace;
}
</style>

<script>
jQuery(document).ready(function($) {
  $('#sql-query-form').on('submit', function(e) {
    e.preventDefault();
    
    var formData = $(this).serialize();
    
    $('#query-results').html('<p>Executing query...</p>');
    
    $.post(ajaxurl, formData, function(response) {
      $('#query-results').html(response);
    }).fail(function() {
      $('#query-results').html('<div class="notice notice-error"><p>Query failed to execute.</p></div>');
    });
  });
});

function loadTableData(table) {
  var query = 'SELECT * FROM `' + table + '` LIMIT 50';
  document.getElementById('sql-query').value = query;
  document.getElementById('sql-query-form').dispatchEvent(new Event('submit'));
}

function showTableStructure(table) {
  var query = 'PRAGMA table_info(`' + table + '`)';
  document.getElementById('sql-query').value = query;
  document.getElementById('sql-query-form').dispatchEvent(new Event('submit'));
}

function runQuickQuery(query) {
  document.getElementById('sql-query').value = query;
  document.getElementById('sql-query-form').dispatchEvent(new Event('submit'));
}

function clearQuery() {
  document.getElementById('sql-query').value = '';
  document.getElementById('query-results').innerHTML = '';
}
</script>

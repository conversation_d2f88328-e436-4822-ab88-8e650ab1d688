<?php
/**
 * Import page for Baum Organizations - MySQL Version
 */

// Prevent direct access
if (!defined('ABSPATH')) {
  exit;
}

// Handle CSV import
if (isset($_POST['import_csv']) && wp_verify_nonce($_POST['_wpnonce'], 'import_csv')) {
  if (isset($_FILES['csv_file']) && $_FILES['csv_file']['error'] === UPLOAD_ERR_OK) {
    $result = import_csv_file($_FILES['csv_file']['tmp_name']);
    
    if ($result['success']) {
      echo '<div class="notice notice-success"><p>' . 
           sprintf(__('Successfully imported %d organizations!', 'baum-organizations'), $result['imported']) . 
           '</p></div>';
    } else {
      echo '<div class="notice notice-error"><p>' . esc_html($result['message']) . '</p></div>';
    }
  } else {
    echo '<div class="notice notice-error"><p>' . __('Please select a valid CSV file.', 'baum-organizations') . '</p></div>';
  }
}

// Handle sample data import
if (isset($_POST['import_sample']) && wp_verify_nonce($_POST['_wpnonce'], 'import_sample')) {
  $result = import_sample_data();
  
  if ($result['success']) {
    echo '<div class="notice notice-success"><p>' . 
         sprintf(__('Successfully imported %d sample organizations!', 'baum-organizations'), $result['imported']) . 
         '</p></div>';
  } else {
    echo '<div class="notice notice-error"><p>' . esc_html($result['message']) . '</p></div>';
  }
}

// Handle clear data
if (isset($_POST['clear_data']) && wp_verify_nonce($_POST['_wpnonce'], 'clear_data')) {
  $result = clear_all_data();
  
  if ($result['success']) {
    echo '<div class="notice notice-success"><p>' . 
         sprintf(__('Successfully cleared %d organizations from database.', 'baum-organizations'), $result['cleared']) . 
         '</p></div>';
  } else {
    echo '<div class="notice notice-error"><p>' . esc_html($result['message']) . '</p></div>';
  }
}

/**
 * Import CSV file
 */
function import_csv_file($file_path) {
  global $wpdb;
  $table_name = $wpdb->prefix . 'baum_organizations';
  
  if (!file_exists($file_path)) {
    return array('success' => false, 'message' => 'File not found');
  }
  
  $handle = fopen($file_path, 'r');
  if (!$handle) {
    return array('success' => false, 'message' => 'Could not open file');
  }
  
  $imported = 0;
  $header = fgetcsv($handle); // Skip header row
  
  while (($data = fgetcsv($handle)) !== FALSE) {
    if (count($data) >= 8) {
      $org_data = array(
        'name' => sanitize_text_field($data[0]),
        'website' => sanitize_text_field($data[1]),
        'industry' => sanitize_text_field($data[2]),
        'size' => sanitize_text_field($data[3]),
        'founded' => intval($data[4]) ?: null,
        'locality' => sanitize_text_field($data[5]),
        'region' => sanitize_text_field($data[6]),
        'country' => sanitize_text_field($data[7]),
        'linkedin_url' => isset($data[8]) ? sanitize_text_field($data[8]) : null,
        'logo_filename' => isset($data[9]) ? sanitize_text_field($data[9]) : null
      );
      
      if (BaumOrganizations::save_organization($org_data)) {
        $imported++;
      }
    }
  }
  
  fclose($handle);
  
  return array('success' => true, 'imported' => $imported);
}

/**
 * Import sample data
 */
function import_sample_data() {
  $sample_data = array(
    array(
      'name' => 'Apple Inc.',
      'website' => 'apple.com',
      'industry' => 'Consumer Electronics',
      'size' => '10001+',
      'founded' => 1976,
      'locality' => 'Cupertino',
      'region' => 'California',
      'country' => 'United States',
      'linkedin_url' => 'linkedin.com/company/apple',
      'logo_filename' => 'apple.png'
    ),
    array(
      'name' => 'Microsoft Corporation',
      'website' => 'microsoft.com',
      'industry' => 'Software',
      'size' => '10001+',
      'founded' => 1975,
      'locality' => 'Redmond',
      'region' => 'Washington',
      'country' => 'United States',
      'linkedin_url' => 'linkedin.com/company/microsoft',
      'logo_filename' => 'microsoft.png'
    ),
    array(
      'name' => 'Google LLC',
      'website' => 'google.com',
      'industry' => 'Internet',
      'size' => '10001+',
      'founded' => 1998,
      'locality' => 'Mountain View',
      'region' => 'California',
      'country' => 'United States',
      'linkedin_url' => 'linkedin.com/company/google',
      'logo_filename' => 'google.png'
    ),
    array(
      'name' => 'Amazon.com Inc.',
      'website' => 'amazon.com',
      'industry' => 'E-commerce',
      'size' => '10001+',
      'founded' => 1994,
      'locality' => 'Seattle',
      'region' => 'Washington',
      'country' => 'United States',
      'linkedin_url' => 'linkedin.com/company/amazon',
      'logo_filename' => 'amazon.png'
    ),
    array(
      'name' => 'Meta Platforms Inc.',
      'website' => 'meta.com',
      'industry' => 'Social Media',
      'size' => '10001+',
      'founded' => 2004,
      'locality' => 'Menlo Park',
      'region' => 'California',
      'country' => 'United States',
      'linkedin_url' => 'linkedin.com/company/meta',
      'logo_filename' => 'meta.png'
    )
  );
  
  $imported = 0;
  
  foreach ($sample_data as $org_data) {
    if (BaumOrganizations::save_organization($org_data)) {
      $imported++;
    }
  }
  
  return array('success' => true, 'imported' => $imported);
}

/**
 * Clear all data
 */
function clear_all_data() {
  global $wpdb;
  $table_name = $wpdb->prefix . 'baum_organizations';
  
  $count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
  $result = $wpdb->query("DELETE FROM $table_name");
  
  if ($result !== false) {
    return array('success' => true, 'cleared' => $count);
  } else {
    return array('success' => false, 'message' => 'Failed to clear data');
  }
}

// Get current statistics
global $wpdb;
$table_name = $wpdb->prefix . 'baum_organizations';
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
$total_orgs = $table_exists ? $wpdb->get_var("SELECT COUNT(*) FROM $table_name") : 0;
?>

<div class="wrap">
  <h1><?php _e('Import Organization Data', 'baum-organizations'); ?></h1>
  
  <div class="notice notice-info">
    <p>
      <strong><?php _e('Data Import Options', 'baum-organizations'); ?></strong><br>
      <?php _e('Import organization data from CSV files or use sample data to get started quickly.', 'baum-organizations'); ?>
    </p>
  </div>
  
  <!-- Current Status -->
  <div class="card">
    <h2><?php _e('Current Status', 'baum-organizations'); ?></h2>
    
    <table class="form-table">
      <tr>
        <th scope="row"><?php _e('Database Table', 'baum-organizations'); ?></th>
        <td>
          <?php if ($table_exists): ?>
            <span class="dashicons dashicons-yes-alt" style="color: #46b450;"></span>
            <?php _e('Created', 'baum-organizations'); ?>
          <?php else: ?>
            <span class="dashicons dashicons-warning" style="color: #ffb900;"></span>
            <?php _e('Not created', 'baum-organizations'); ?>
          <?php endif; ?>
        </td>
      </tr>
      <tr>
        <th scope="row"><?php _e('Total Organizations', 'baum-organizations'); ?></th>
        <td><strong><?php echo number_format($total_orgs); ?></strong></td>
      </tr>
    </table>
  </div>
  
  <!-- CSV Import -->
  <div class="card">
    <h2><?php _e('CSV Import', 'baum-organizations'); ?></h2>
    
    <p><?php _e('Upload a CSV file with organization data. The CSV should have the following columns:', 'baum-organizations'); ?></p>
    
    <ul>
      <li><strong>name</strong> - <?php _e('Organization name (required)', 'baum-organizations'); ?></li>
      <li><strong>website</strong> - <?php _e('Website URL', 'baum-organizations'); ?></li>
      <li><strong>industry</strong> - <?php _e('Industry category', 'baum-organizations'); ?></li>
      <li><strong>size</strong> - <?php _e('Company size (e.g., "51-200", "1001-5000")', 'baum-organizations'); ?></li>
      <li><strong>founded</strong> - <?php _e('Year founded', 'baum-organizations'); ?></li>
      <li><strong>locality</strong> - <?php _e('City', 'baum-organizations'); ?></li>
      <li><strong>region</strong> - <?php _e('State/Province', 'baum-organizations'); ?></li>
      <li><strong>country</strong> - <?php _e('Country', 'baum-organizations'); ?></li>
      <li><strong>linkedin_url</strong> - <?php _e('LinkedIn URL (optional)', 'baum-organizations'); ?></li>
      <li><strong>logo_filename</strong> - <?php _e('Logo filename (optional)', 'baum-organizations'); ?></li>
    </ul>
    
    <form method="post" enctype="multipart/form-data">
      <?php wp_nonce_field('import_csv'); ?>
      
      <table class="form-table">
        <tr>
          <th scope="row">
            <label for="csv_file"><?php _e('CSV File', 'baum-organizations'); ?></label>
          </th>
          <td>
            <input type="file" id="csv_file" name="csv_file" accept=".csv" required>
            <p class="description"><?php _e('Select a CSV file to import.', 'baum-organizations'); ?></p>
          </td>
        </tr>
      </table>
      
      <p class="submit">
        <input type="submit" name="import_csv" class="button-primary" 
               value="<?php _e('Import CSV', 'baum-organizations'); ?>">
      </p>
    </form>
  </div>
  
  <!-- Sample Data -->
  <div class="card">
    <h2><?php _e('Sample Data', 'baum-organizations'); ?></h2>
    
    <p><?php _e('Import sample organization data to test the plugin functionality.', 'baum-organizations'); ?></p>
    
    <form method="post">
      <?php wp_nonce_field('import_sample'); ?>
      
      <p class="submit">
        <input type="submit" name="import_sample" class="button-secondary" 
               value="<?php _e('Import Sample Data', 'baum-organizations'); ?>"
               onclick="return confirm('<?php _e('This will add sample organizations. Continue?', 'baum-organizations'); ?>')">
      </p>
    </form>
  </div>
  
  <!-- Clear Data -->
  <?php if ($total_orgs > 0): ?>
    <div class="card">
      <h2><?php _e('Clear Data', 'baum-organizations'); ?></h2>
      
      <p><?php _e('Remove all organization data from the database.', 'baum-organizations'); ?></p>
      
      <form method="post">
        <?php wp_nonce_field('clear_data'); ?>
        
        <p class="submit">
          <input type="submit" name="clear_data" class="button button-secondary" 
                 value="<?php _e('Clear All Data', 'baum-organizations'); ?>"
                 onclick="return confirm('<?php _e('This will permanently delete all organization data. Are you sure?', 'baum-organizations'); ?>')">
        </p>
      </form>
    </div>
  <?php endif; ?>
</div>

<style>
.card {
  background: #fff;
  border: 1px solid #ccd0d4;
  border-radius: 4px;
  padding: 20px;
  margin: 20px 0;
  box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.card h2 {
  margin-top: 0;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.card ul {
  background: #f9f9f9;
  padding: 15px 20px;
  border-left: 4px solid #0073aa;
  margin: 15px 0;
}

.card ul li {
  margin-bottom: 5px;
}
</style>

<?php
/**
 * Import page for Baum Organizations
 */

// Prevent direct access
if (!defined('ABSPATH')) {
  exit;
}

// Handle import action
$import_status = '';
$import_message = '';

if (isset($_POST['start_import']) && wp_verify_nonce($_POST['import_nonce'], 'baum_orgs_import')) {
  $script_path = BAUM_ORGS_PLUGIN_DIR . 'scripts/convert-dataset.php';
  
  if (file_exists($script_path)) {
    // Execute the conversion script
    $output = array();
    $return_var = 0;
    
    // Change to plugin directory and run the script
    $old_cwd = getcwd();
    chdir(BAUM_ORGS_PLUGIN_DIR);
    
    exec("php scripts/convert-dataset.php 2>&1", $output, $return_var);
    
    chdir($old_cwd);
    
    if ($return_var === 0) {
      $import_status = 'success';
      $import_message = __('Import completed successfully!', 'baum-organizations');
      update_option('baum_orgs_db_imported', true);
    } else {
      $import_status = 'error';
      $import_message = __('Import failed. Please check the error log.', 'baum-organizations') . '<br><pre>' . implode("\n", $output) . '</pre>';
    }
  } else {
    $import_status = 'error';
    $import_message = __('Import script not found.', 'baum-organizations');
  }
}

// Check current status
$json_file_exists = file_exists(BAUM_ORGS_PLUGIN_DIR . 'company-dataset.json');
$db_exists = file_exists(BAUM_ORGS_DB_PATH);
$logos_dir_exists = file_exists(BAUM_ORGS_LOGOS_DIR);
$script_exists = file_exists(BAUM_ORGS_PLUGIN_DIR . 'scripts/convert-dataset.php');

// Get file sizes and counts
$json_file_size = $json_file_exists ? filesize(BAUM_ORGS_PLUGIN_DIR . 'company-dataset.json') : 0;
$logos_count = 0;
if ($logos_dir_exists) {
  $logos_count = count(glob(BAUM_ORGS_LOGOS_DIR . '*.svg'));
}

// Get database stats if it exists
$db_stats = array();
if ($db_exists) {
  $db = BaumOrganizations::get_db_connection();
  if ($db) {
    try {
      $stmt = $db->query("SELECT COUNT(*) as total FROM organizations");
      $result = $stmt->fetch(PDO::FETCH_ASSOC);
      $db_stats['total'] = $result['total'];
      
      $stmt = $db->query("SELECT COUNT(*) as total FROM organizations WHERE logo_filename IS NOT NULL");
      $result = $stmt->fetch(PDO::FETCH_ASSOC);
      $db_stats['with_logos'] = $result['total'];
      
      $stmt = $db->query("SELECT COUNT(*) as total FROM organizations WHERE website IS NOT NULL");
      $result = $stmt->fetch(PDO::FETCH_ASSOC);
      $db_stats['with_websites'] = $result['total'];
    } catch (PDOException $e) {
      error_log('Database stats error: ' . $e->getMessage());
    }
  }
}

function format_bytes($size, $precision = 2) {
  $base = log($size, 1024);
  $suffixes = array('B', 'KB', 'MB', 'GB', 'TB');
  return round(pow(1024, $base - floor($base)), $precision) . ' ' . $suffixes[floor($base)];
}
?>

<div class="wrap">
  <h1><?php _e('Import Organization Data', 'baum-organizations'); ?></h1>
  
  <?php if ($import_status): ?>
    <div class="notice notice-<?php echo $import_status === 'success' ? 'success' : 'error'; ?>">
      <p><?php echo $import_message; ?></p>
    </div>
  <?php endif; ?>
  
  <!-- System Status -->
  <div class="card">
    <h2><?php _e('System Status', 'baum-organizations'); ?></h2>
    <table class="form-table">
      <tr>
        <th scope="row"><?php _e('JSON Dataset File', 'baum-organizations'); ?></th>
        <td>
          <?php if ($json_file_exists): ?>
            <span class="dashicons dashicons-yes-alt" style="color: green;"></span>
            <?php printf(__('Found (%s)', 'baum-organizations'), format_bytes($json_file_size)); ?>
          <?php else: ?>
            <span class="dashicons dashicons-dismiss" style="color: red;"></span>
            <?php _e('Not found - Please place company-dataset.json in the plugin directory', 'baum-organizations'); ?>
          <?php endif; ?>
        </td>
      </tr>
      
      <tr>
        <th scope="row"><?php _e('Logos Directory', 'baum-organizations'); ?></th>
        <td>
          <?php if ($logos_dir_exists): ?>
            <span class="dashicons dashicons-yes-alt" style="color: green;"></span>
            <?php printf(__('Found (%d logos)', 'baum-organizations'), $logos_count); ?>
          <?php else: ?>
            <span class="dashicons dashicons-dismiss" style="color: red;"></span>
            <?php _e('Not found', 'baum-organizations'); ?>
          <?php endif; ?>
        </td>
      </tr>
      
      <tr>
        <th scope="row"><?php _e('Conversion Script', 'baum-organizations'); ?></th>
        <td>
          <?php if ($script_exists): ?>
            <span class="dashicons dashicons-yes-alt" style="color: green;"></span>
            <?php _e('Available', 'baum-organizations'); ?>
          <?php else: ?>
            <span class="dashicons dashicons-dismiss" style="color: red;"></span>
            <?php _e('Not found', 'baum-organizations'); ?>
          <?php endif; ?>
        </td>
      </tr>
      
      <tr>
        <th scope="row"><?php _e('SQLite Database', 'baum-organizations'); ?></th>
        <td>
          <?php if ($db_exists): ?>
            <span class="dashicons dashicons-yes-alt" style="color: green;"></span>
            <?php _e('Created', 'baum-organizations'); ?>
            <?php if (!empty($db_stats)): ?>
              <br>
              <small>
                <?php printf(__('Total organizations: %s', 'baum-organizations'), number_format($db_stats['total'])); ?><br>
                <?php printf(__('With logos: %s', 'baum-organizations'), number_format($db_stats['with_logos'])); ?><br>
                <?php printf(__('With websites: %s', 'baum-organizations'), number_format($db_stats['with_websites'])); ?>
              </small>
            <?php endif; ?>
          <?php else: ?>
            <span class="dashicons dashicons-minus" style="color: orange;"></span>
            <?php _e('Not created yet', 'baum-organizations'); ?>
          <?php endif; ?>
        </td>
      </tr>
      
      <tr>
        <th scope="row"><?php _e('PHP Memory Limit', 'baum-organizations'); ?></th>
        <td>
          <?php 
          $memory_limit = ini_get('memory_limit');
          $memory_bytes = wp_convert_hr_to_bytes($memory_limit);
          $recommended_bytes = wp_convert_hr_to_bytes('2G');
          ?>
          <?php if ($memory_bytes >= $recommended_bytes): ?>
            <span class="dashicons dashicons-yes-alt" style="color: green;"></span>
          <?php else: ?>
            <span class="dashicons dashicons-warning" style="color: orange;"></span>
          <?php endif; ?>
          <?php echo $memory_limit; ?>
          <?php if ($memory_bytes < $recommended_bytes): ?>
            <br><small><?php _e('Recommended: 2G or higher for large datasets', 'baum-organizations'); ?></small>
          <?php endif; ?>
        </td>
      </tr>
      
      <tr>
        <th scope="row"><?php _e('PHP Max Execution Time', 'baum-organizations'); ?></th>
        <td>
          <?php 
          $max_execution_time = ini_get('max_execution_time');
          ?>
          <?php if ($max_execution_time == 0 || $max_execution_time >= 300): ?>
            <span class="dashicons dashicons-yes-alt" style="color: green;"></span>
          <?php else: ?>
            <span class="dashicons dashicons-warning" style="color: orange;"></span>
          <?php endif; ?>
          <?php echo $max_execution_time == 0 ? __('Unlimited', 'baum-organizations') : $max_execution_time . 's'; ?>
          <?php if ($max_execution_time > 0 && $max_execution_time < 300): ?>
            <br><small><?php _e('Recommended: 300s or unlimited for large datasets', 'baum-organizations'); ?></small>
          <?php endif; ?>
        </td>
      </tr>
    </table>
  </div>
  
  <!-- Import Instructions -->
  <div class="card">
    <h2><?php _e('Import Instructions', 'baum-organizations'); ?></h2>
    <ol>
      <li><?php _e('Ensure the company-dataset.json file is placed in the plugin directory', 'baum-organizations'); ?></li>
      <li><?php _e('Make sure the logos directory contains the SVG logo files', 'baum-organizations'); ?></li>
      <li><?php _e('Click the "Start Import" button below to begin the conversion process', 'baum-organizations'); ?></li>
      <li><?php _e('The import process may take several minutes for large datasets', 'baum-organizations'); ?></li>
      <li><?php _e('Once completed, you can browse organizations and use the database manager', 'baum-organizations'); ?></li>
    </ol>
    
    <div class="notice notice-info inline">
      <p>
        <strong><?php _e('Note:', 'baum-organizations'); ?></strong>
        <?php _e('The import process will create a SQLite database and automatically match logo filenames with organization names. This may take some time depending on the size of your dataset.', 'baum-organizations'); ?>
      </p>
    </div>
  </div>
  
  <!-- Import Form -->
  <?php if ($json_file_exists && $logos_dir_exists && $script_exists): ?>
    <div class="card">
      <h2><?php _e('Start Import', 'baum-organizations'); ?></h2>
      
      <?php if ($db_exists): ?>
        <div class="notice notice-warning inline">
          <p>
            <strong><?php _e('Warning:', 'baum-organizations'); ?></strong>
            <?php _e('A database already exists. Running the import will replace the existing data.', 'baum-organizations'); ?>
          </p>
        </div>
      <?php endif; ?>
      
      <form method="post" action="">
        <?php wp_nonce_field('baum_orgs_import', 'import_nonce'); ?>
        <p>
          <input type="submit" name="start_import" class="button button-primary" 
                 value="<?php _e('Start Import', 'baum-organizations'); ?>"
                 onclick="return confirm('<?php _e('Are you sure you want to start the import? This may take several minutes.', 'baum-organizations'); ?>');">
        </p>
      </form>
    </div>
  <?php else: ?>
    <div class="notice notice-error">
      <p>
        <strong><?php _e('Cannot start import:', 'baum-organizations'); ?></strong>
        <?php _e('Please ensure all required files are in place before starting the import.', 'baum-organizations'); ?>
      </p>
    </div>
  <?php endif; ?>
  
  <!-- Manual Script Execution -->
  <div class="card">
    <h2><?php _e('Manual Script Execution', 'baum-organizations'); ?></h2>
    <p><?php _e('You can also run the conversion script manually from the command line:', 'baum-organizations'); ?></p>
    <pre><code>cd <?php echo esc_html(BAUM_ORGS_PLUGIN_DIR); ?>
php scripts/convert-dataset.php</code></pre>
    
    <p><?php _e('This method may be more reliable for very large datasets and provides detailed progress information.', 'baum-organizations'); ?></p>
  </div>
  
  <!-- Troubleshooting -->
  <div class="card">
    <h2><?php _e('Troubleshooting', 'baum-organizations'); ?></h2>
    <ul>
      <li><strong><?php _e('Memory errors:', 'baum-organizations'); ?></strong> <?php _e('Increase PHP memory_limit to 2G or higher', 'baum-organizations'); ?></li>
      <li><strong><?php _e('Timeout errors:', 'baum-organizations'); ?></strong> <?php _e('Increase max_execution_time or set to 0 (unlimited)', 'baum-organizations'); ?></li>
      <li><strong><?php _e('Permission errors:', 'baum-organizations'); ?></strong> <?php _e('Ensure the plugin directory is writable', 'baum-organizations'); ?></li>
      <li><strong><?php _e('Large datasets:', 'baum-organizations'); ?></strong> <?php _e('Consider running the script via command line for better performance', 'baum-organizations'); ?></li>
    </ul>
  </div>
</div>

<style>
.card {
  background: #fff;
  border: 1px solid #ccd0d4;
  border-radius: 4px;
  padding: 20px;
  margin: 20px 0;
  box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.card h2 {
  margin-top: 0;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.notice.inline {
  margin: 15px 0;
  padding: 12px;
}

pre code {
  background: #f1f1f1;
  padding: 10px;
  display: block;
  border-radius: 4px;
  font-family: Consolas, Monaco, monospace;
}

.form-table th {
  width: 200px;
}

.dashicons {
  font-size: 16px;
  width: 16px;
  height: 16px;
}
</style>

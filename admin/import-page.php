<?php
/**
 * Import page for Baum Organizations - MySQL Version
 */

// Prevent direct access
if (!defined('ABSPATH')) {
  exit;
}

// Handle JSON import
if (isset($_POST['import_json']) && wp_verify_nonce($_POST['_wpnonce'], 'import_json')) {
  $result = import_json_dataset();

  if ($result['success']) {
    echo '<div class="notice notice-success"><p>' .
         sprintf(__('Successfully imported %d organizations from JSON dataset!', 'baum-organizations'), $result['imported']) .
         '</p></div>';
  } else {
    echo '<div class="notice notice-error"><p>' . esc_html($result['message']) . '</p></div>';
  }
}

// Handle CSV import
if (isset($_POST['import_csv']) && wp_verify_nonce($_POST['_wpnonce'], 'import_csv')) {
  if (isset($_FILES['csv_file']) && $_FILES['csv_file']['error'] === UPLOAD_ERR_OK) {
    $result = import_csv_file($_FILES['csv_file']['tmp_name']);

    if ($result['success']) {
      echo '<div class="notice notice-success"><p>' .
           sprintf(__('Successfully imported %d organizations!', 'baum-organizations'), $result['imported']) .
           '</p></div>';
    } else {
      echo '<div class="notice notice-error"><p>' . esc_html($result['message']) . '</p></div>';
    }
  } else {
    echo '<div class="notice notice-error"><p>' . __('Please select a valid CSV file.', 'baum-organizations') . '</p></div>';
  }
}

// Handle sample data import
if (isset($_POST['import_sample']) && wp_verify_nonce($_POST['_wpnonce'], 'import_sample')) {
  $result = import_sample_data();
  
  if ($result['success']) {
    echo '<div class="notice notice-success"><p>' . 
         sprintf(__('Successfully imported %d sample organizations!', 'baum-organizations'), $result['imported']) . 
         '</p></div>';
  } else {
    echo '<div class="notice notice-error"><p>' . esc_html($result['message']) . '</p></div>';
  }
}

// Handle clear data
if (isset($_POST['clear_data']) && wp_verify_nonce($_POST['_wpnonce'], 'clear_data')) {
  $result = clear_all_data();
  
  if ($result['success']) {
    echo '<div class="notice notice-success"><p>' . 
         sprintf(__('Successfully cleared %d organizations from database.', 'baum-organizations'), $result['cleared']) . 
         '</p></div>';
  } else {
    echo '<div class="notice notice-error"><p>' . esc_html($result['message']) . '</p></div>';
  }
}

/**
 * Import JSON dataset from company-dataset.json
 */
function import_json_dataset() {
  global $wpdb;
  $table_name = $wpdb->prefix . 'baum_organizations';

  $json_file = BAUM_ORGS_PLUGIN_DIR . 'company-dataset.json';

  if (!file_exists($json_file)) {
    return array('success' => false, 'message' => 'JSON dataset file not found: ' . $json_file);
  }

  $handle = fopen($json_file, 'r');
  if (!$handle) {
    return array('success' => false, 'message' => 'Could not open JSON file');
  }

  $imported = 0;
  $batch_size = 100; // Process in batches for better performance
  $batch_data = array();

  while (($line = fgets($handle)) !== false) {
    $line = trim($line);
    if (empty($line)) continue;

    $org_json = json_decode($line, true);
    if (!$org_json || !isset($org_json['name'])) {
      continue; // Skip invalid JSON lines
    }

    $org_data = array(
      'name' => sanitize_text_field($org_json['name']),
      'website' => !empty($org_json['website']) ? sanitize_text_field($org_json['website']) : null,
      'industry' => !empty($org_json['industry']) ? sanitize_text_field($org_json['industry']) : null,
      'size' => !empty($org_json['size']) ? sanitize_text_field($org_json['size']) : null,
      'founded' => !empty($org_json['founded']) ? intval($org_json['founded']) : null,
      'locality' => !empty($org_json['locality']) ? sanitize_text_field($org_json['locality']) : null,
      'region' => !empty($org_json['region']) ? sanitize_text_field($org_json['region']) : null,
      'country' => !empty($org_json['country']) ? sanitize_text_field($org_json['country']) : null,
      'linkedin_url' => !empty($org_json['linkedin_url']) ? sanitize_text_field($org_json['linkedin_url']) : null,
      'logo_filename' => !empty($org_json['logo_filename']) ? sanitize_text_field($org_json['logo_filename']) : null
    );

    $batch_data[] = $org_data;

    // Process batch when it reaches batch_size
    if (count($batch_data) >= $batch_size) {
      $imported += process_batch($batch_data);
      $batch_data = array();
    }
  }

  // Process remaining data
  if (!empty($batch_data)) {
    $imported += process_batch($batch_data);
  }

  fclose($handle);

  return array('success' => true, 'imported' => $imported);
}

/**
 * Process a batch of organization data
 */
function process_batch($batch_data) {
  $imported = 0;

  foreach ($batch_data as $org_data) {
    if (BaumOrganizations::save_organization($org_data)) {
      $imported++;
    }
  }

  return $imported;
}

/**
 * Import CSV file
 */
function import_csv_file($file_path) {
  global $wpdb;
  $table_name = $wpdb->prefix . 'baum_organizations';
  
  if (!file_exists($file_path)) {
    return array('success' => false, 'message' => 'File not found');
  }
  
  $handle = fopen($file_path, 'r');
  if (!$handle) {
    return array('success' => false, 'message' => 'Could not open file');
  }
  
  $imported = 0;
  $header = fgetcsv($handle); // Skip header row
  
  while (($data = fgetcsv($handle)) !== FALSE) {
    if (count($data) >= 8) {
      $org_data = array(
        'name' => sanitize_text_field($data[0]),
        'website' => sanitize_text_field($data[1]),
        'industry' => sanitize_text_field($data[2]),
        'size' => sanitize_text_field($data[3]),
        'founded' => intval($data[4]) ?: null,
        'locality' => sanitize_text_field($data[5]),
        'region' => sanitize_text_field($data[6]),
        'country' => sanitize_text_field($data[7]),
        'linkedin_url' => isset($data[8]) ? sanitize_text_field($data[8]) : null,
        'logo_filename' => isset($data[9]) ? sanitize_text_field($data[9]) : null
      );
      
      if (BaumOrganizations::save_organization($org_data)) {
        $imported++;
      }
    }
  }
  
  fclose($handle);
  
  return array('success' => true, 'imported' => $imported);
}

/**
 * Import sample data
 */
function import_sample_data() {
  $sample_data = array(
    array(
      'name' => 'Apple Inc.',
      'website' => 'apple.com',
      'industry' => 'Consumer Electronics',
      'size' => '10001+',
      'founded' => 1976,
      'locality' => 'Cupertino',
      'region' => 'California',
      'country' => 'United States',
      'linkedin_url' => 'linkedin.com/company/apple',
      'logo_filename' => 'apple.png'
    ),
    array(
      'name' => 'Microsoft Corporation',
      'website' => 'microsoft.com',
      'industry' => 'Software',
      'size' => '10001+',
      'founded' => 1975,
      'locality' => 'Redmond',
      'region' => 'Washington',
      'country' => 'United States',
      'linkedin_url' => 'linkedin.com/company/microsoft',
      'logo_filename' => 'microsoft.png'
    ),
    array(
      'name' => 'Google LLC',
      'website' => 'google.com',
      'industry' => 'Internet',
      'size' => '10001+',
      'founded' => 1998,
      'locality' => 'Mountain View',
      'region' => 'California',
      'country' => 'United States',
      'linkedin_url' => 'linkedin.com/company/google',
      'logo_filename' => 'google.png'
    ),
    array(
      'name' => 'Amazon.com Inc.',
      'website' => 'amazon.com',
      'industry' => 'E-commerce',
      'size' => '10001+',
      'founded' => 1994,
      'locality' => 'Seattle',
      'region' => 'Washington',
      'country' => 'United States',
      'linkedin_url' => 'linkedin.com/company/amazon',
      'logo_filename' => 'amazon.png'
    ),
    array(
      'name' => 'Meta Platforms Inc.',
      'website' => 'meta.com',
      'industry' => 'Social Media',
      'size' => '10001+',
      'founded' => 2004,
      'locality' => 'Menlo Park',
      'region' => 'California',
      'country' => 'United States',
      'linkedin_url' => 'linkedin.com/company/meta',
      'logo_filename' => 'meta.png'
    )
  );
  
  $imported = 0;
  
  foreach ($sample_data as $org_data) {
    if (BaumOrganizations::save_organization($org_data)) {
      $imported++;
    }
  }
  
  return array('success' => true, 'imported' => $imported);
}

/**
 * Clear all data
 */
function clear_all_data() {
  global $wpdb;
  $table_name = $wpdb->prefix . 'baum_organizations';
  
  $count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
  $result = $wpdb->query("DELETE FROM $table_name");
  
  if ($result !== false) {
    return array('success' => true, 'cleared' => $count);
  } else {
    return array('success' => false, 'message' => 'Failed to clear data');
  }
}

// Get current statistics
global $wpdb;
$table_name = $wpdb->prefix . 'baum_organizations';
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
$total_orgs = $table_exists ? $wpdb->get_var("SELECT COUNT(*) FROM $table_name") : 0;
?>

<div class="wrap">
  <h1><?php _e('Import Organization Data', 'baum-organizations'); ?></h1>
  
  <div class="notice notice-info">
    <p>
      <strong><?php _e('Data Import Options', 'baum-organizations'); ?></strong><br>
      <?php _e('Import organization data from CSV files or use sample data to get started quickly.', 'baum-organizations'); ?>
    </p>
  </div>
  
  <!-- Current Status -->
  <div class="card">
    <h2><?php _e('Current Status', 'baum-organizations'); ?></h2>
    
    <table class="form-table">
      <tr>
        <th scope="row"><?php _e('Database Table', 'baum-organizations'); ?></th>
        <td>
          <?php if ($table_exists): ?>
            <span class="dashicons dashicons-yes-alt" style="color: #46b450;"></span>
            <?php _e('Created', 'baum-organizations'); ?>
          <?php else: ?>
            <span class="dashicons dashicons-warning" style="color: #ffb900;"></span>
            <?php _e('Not created', 'baum-organizations'); ?>
          <?php endif; ?>
        </td>
      </tr>
      <tr>
        <th scope="row"><?php _e('Total Organizations', 'baum-organizations'); ?></th>
        <td><strong><?php echo number_format($total_orgs); ?></strong></td>
      </tr>
    </table>
  </div>

  <!-- JSON Dataset Import -->
  <div class="card">
    <h2><?php _e('Import JSON Dataset', 'baum-organizations'); ?></h2>

    <p><?php _e('Import the complete organization dataset from the company-dataset.json file.', 'baum-organizations'); ?></p>

    <?php
    $json_file = BAUM_ORGS_PLUGIN_DIR . 'company-dataset.json';
    $json_exists = file_exists($json_file);
    $json_size = $json_exists ? filesize($json_file) : 0;
    $estimated_records = $json_exists ? (int)($json_size / 200) : 0; // Rough estimate
    ?>

    <table class="form-table">
      <tr>
        <th scope="row"><?php _e('Dataset File', 'baum-organizations'); ?></th>
        <td>
          <?php if ($json_exists): ?>
            <span class="dashicons dashicons-yes-alt" style="color: #46b450;"></span>
            <code>company-dataset.json</code>
            <p class="description">
              <?php printf(__('File size: %s | Estimated records: %s', 'baum-organizations'),
                          size_format($json_size),
                          number_format($estimated_records)); ?>
            </p>
          <?php else: ?>
            <span class="dashicons dashicons-warning" style="color: #ffb900;"></span>
            <?php _e('File not found', 'baum-organizations'); ?>
            <p class="description"><?php _e('Please ensure company-dataset.json is in the plugin directory.', 'baum-organizations'); ?></p>
          <?php endif; ?>
        </td>
      </tr>
    </table>

    <?php if ($json_exists): ?>
      <form method="post">
        <?php wp_nonce_field('import_json'); ?>

        <div class="notice notice-info inline">
          <p>
            <strong><?php _e('Note:', 'baum-organizations'); ?></strong>
            <?php _e('This will import all organizations from the JSON dataset. This may take several minutes for large datasets.', 'baum-organizations'); ?>
          </p>
        </div>

        <p class="submit">
          <input type="submit" name="import_json" class="button-primary" id="import-json-btn"
                 value="<?php _e('Import JSON Dataset', 'baum-organizations'); ?>"
                 onclick="return startJsonImport()">
        </p>

        <div id="import-progress" style="display: none;">
          <p><?php _e('Importing organizations...', 'baum-organizations'); ?></p>
          <div class="progress-bar" style="background: #f0f0f0; height: 20px; border-radius: 10px; overflow: hidden;">
            <div id="progress-fill" style="background: #0073aa; height: 100%; width: 0%; transition: width 0.3s ease;"></div>
          </div>
          <p id="progress-text"><?php _e('Starting import...', 'baum-organizations'); ?></p>
        </div>
      </form>
    <?php endif; ?>
  </div>

  <!-- CSV Import -->
  <div class="card">
    <h2><?php _e('Custom CSV Import', 'baum-organizations'); ?></h2>

    <p><?php _e('Upload your own CSV file with organization data. The CSV should have the following columns:', 'baum-organizations'); ?></p>
    
    <ul>
      <li><strong>name</strong> - <?php _e('Organization name (required)', 'baum-organizations'); ?></li>
      <li><strong>website</strong> - <?php _e('Website URL', 'baum-organizations'); ?></li>
      <li><strong>industry</strong> - <?php _e('Industry category', 'baum-organizations'); ?></li>
      <li><strong>size</strong> - <?php _e('Company size (e.g., "51-200", "1001-5000")', 'baum-organizations'); ?></li>
      <li><strong>founded</strong> - <?php _e('Year founded', 'baum-organizations'); ?></li>
      <li><strong>locality</strong> - <?php _e('City', 'baum-organizations'); ?></li>
      <li><strong>region</strong> - <?php _e('State/Province', 'baum-organizations'); ?></li>
      <li><strong>country</strong> - <?php _e('Country', 'baum-organizations'); ?></li>
      <li><strong>linkedin_url</strong> - <?php _e('LinkedIn URL (optional)', 'baum-organizations'); ?></li>
      <li><strong>logo_filename</strong> - <?php _e('Logo filename (optional)', 'baum-organizations'); ?></li>
    </ul>
    
    <form method="post" enctype="multipart/form-data">
      <?php wp_nonce_field('import_csv'); ?>
      
      <table class="form-table">
        <tr>
          <th scope="row">
            <label for="csv_file"><?php _e('CSV File', 'baum-organizations'); ?></label>
          </th>
          <td>
            <input type="file" id="csv_file" name="csv_file" accept=".csv" required>
            <p class="description"><?php _e('Select a CSV file to import.', 'baum-organizations'); ?></p>
          </td>
        </tr>
      </table>
      
      <p class="submit">
        <input type="submit" name="import_csv" class="button-primary" 
               value="<?php _e('Import CSV', 'baum-organizations'); ?>">
      </p>
    </form>
  </div>
  
  <!-- Sample Data -->
  <div class="card">
    <h2><?php _e('Sample Data', 'baum-organizations'); ?></h2>
    
    <p><?php _e('Import sample organization data to test the plugin functionality.', 'baum-organizations'); ?></p>
    
    <form method="post">
      <?php wp_nonce_field('import_sample'); ?>
      
      <p class="submit">
        <input type="submit" name="import_sample" class="button-secondary" 
               value="<?php _e('Import Sample Data', 'baum-organizations'); ?>"
               onclick="return confirm('<?php _e('This will add sample organizations. Continue?', 'baum-organizations'); ?>')">
      </p>
    </form>
  </div>
  
  <!-- Clear Data -->
  <?php if ($total_orgs > 0): ?>
    <div class="card">
      <h2><?php _e('Clear Data', 'baum-organizations'); ?></h2>
      
      <p><?php _e('Remove all organization data from the database.', 'baum-organizations'); ?></p>
      
      <form method="post">
        <?php wp_nonce_field('clear_data'); ?>
        
        <p class="submit">
          <input type="submit" name="clear_data" class="button button-secondary" 
                 value="<?php _e('Clear All Data', 'baum-organizations'); ?>"
                 onclick="return confirm('<?php _e('This will permanently delete all organization data. Are you sure?', 'baum-organizations'); ?>')">
        </p>
      </form>
    </div>
  <?php endif; ?>
</div>

<style>
.card {
  background: #fff;
  border: 1px solid #ccd0d4;
  border-radius: 4px;
  padding: 20px;
  margin: 20px 0;
  box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.card h2 {
  margin-top: 0;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.card ul {
  background: #f9f9f9;
  padding: 15px 20px;
  border-left: 4px solid #0073aa;
  margin: 15px 0;
}

.card ul li {
  margin-bottom: 5px;
}

#import-progress {
  margin-top: 20px;
  padding: 15px;
  background: #f9f9f9;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.progress-bar {
  position: relative;
  margin: 10px 0;
}

#progress-text {
  font-size: 14px;
  color: #666;
  margin: 10px 0 0 0;
}
</style>

<script>
function startJsonImport() {
  if (!confirm('<?php _e('This will import the complete dataset. This may take several minutes. Continue?', 'baum-organizations'); ?>')) {
    return false;
  }

  // Show progress indicator
  document.getElementById('import-json-btn').style.display = 'none';
  document.getElementById('import-progress').style.display = 'block';

  // Simulate progress (since we can't track real progress easily)
  let progress = 0;
  const progressFill = document.getElementById('progress-fill');
  const progressText = document.getElementById('progress-text');

  const interval = setInterval(() => {
    progress += Math.random() * 10;
    if (progress > 90) progress = 90; // Don't go to 100% until actually done

    progressFill.style.width = progress + '%';
    progressText.textContent = '<?php _e('Processing...', 'baum-organizations'); ?> ' + Math.round(progress) + '%';
  }, 500);

  // Store interval ID to clear it when form submits
  window.importInterval = interval;

  return true;
}

// Clear interval when page unloads (form submission)
window.addEventListener('beforeunload', function() {
  if (window.importInterval) {
    clearInterval(window.importInterval);
  }
});
</script>

<?php
/**
 * Database manager page for Baum Organizations
 * Secure wrapper for <PERSON><PERSON><PERSON> with authentication
 */

// Prevent direct access
if (!defined('ABSPATH')) {
  exit;
}

// Database manager credentials (change these!)
define('BAUM_ORGS_DB_USER', 'admin');
define('BAUM_ORGS_DB_PASS', 'BaumOrgs2024!');

// Handle logout
if (isset($_GET['logout'])) {
  unset($_SESSION['baum_orgs_db_authenticated']);
  wp_redirect(admin_url('admin.php?page=baum-organizations-db'));
  exit;
}

// Handle authentication
$authenticated = false;
if (isset($_SESSION['baum_orgs_db_authenticated']) && $_SESSION['baum_orgs_db_authenticated'] === true) {
  $authenticated = true;
}

if (!$authenticated && isset($_POST['login'])) {
  $username = sanitize_text_field($_POST['username']);
  $password = $_POST['password'];
  
  if ($username === BAUM_ORGS_DB_USER && $password === BAUM_ORGS_DB_PASS) {
    $_SESSION['baum_orgs_db_authenticated'] = true;
    $authenticated = true;
  } else {
    $login_error = __('Invalid credentials', 'baum-organizations');
  }
}

// If not authenticated, show login form
if (!$authenticated) {
  ?>
  <div class="wrap">
    <h1><?php _e('Database Manager', 'baum-organizations'); ?></h1>
    
    <div class="card" style="max-width: 400px;">
      <h2><?php _e('Authentication Required', 'baum-organizations'); ?></h2>
      
      <?php if (isset($login_error)): ?>
        <div class="notice notice-error">
          <p><?php echo esc_html($login_error); ?></p>
        </div>
      <?php endif; ?>
      
      <form method="post" action="">
        <table class="form-table">
          <tr>
            <th scope="row">
              <label for="username"><?php _e('Username', 'baum-organizations'); ?></label>
            </th>
            <td>
              <input type="text" id="username" name="username" class="regular-text" required>
            </td>
          </tr>
          <tr>
            <th scope="row">
              <label for="password"><?php _e('Password', 'baum-organizations'); ?></label>
            </th>
            <td>
              <input type="password" id="password" name="password" class="regular-text" required>
            </td>
          </tr>
        </table>
        
        <p class="submit">
          <input type="submit" name="login" class="button button-primary" 
                 value="<?php _e('Login', 'baum-organizations'); ?>">
        </p>
      </form>
      
      <div class="notice notice-info inline">
        <p>
          <strong><?php _e('Default Credentials:', 'baum-organizations'); ?></strong><br>
          <?php _e('Username:', 'baum-organizations'); ?> <code>admin</code><br>
          <?php _e('Password:', 'baum-organizations'); ?> <code>BaumOrgs2024!</code>
        </p>
        <p>
          <small><?php _e('Please change these credentials in the plugin code for security.', 'baum-organizations'); ?></small>
        </p>
      </div>
    </div>
  </div>
  
  <style>
  .card {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin: 20px 0;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
  }
  
  .card h2 {
    margin-top: 0;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
  }
  
  .notice.inline {
    margin: 15px 0;
    padding: 12px;
  }
  </style>
  <?php
  return;
}

// Check if database exists
if (!file_exists(BAUM_ORGS_DB_PATH)) {
  ?>
  <div class="wrap">
    <h1><?php _e('Database Manager', 'baum-organizations'); ?></h1>
    
    <div class="notice notice-warning">
      <p>
        <?php _e('Database not found. Please import the organization data first.', 'baum-organizations'); ?>
        <a href="<?php echo admin_url('admin.php?page=baum-organizations-import'); ?>" class="button">
          <?php _e('Import Data', 'baum-organizations'); ?>
        </a>
      </p>
    </div>
  </div>
  <?php
  return;
}

// Handle Adminer request
if (isset($_GET['adminer'])) {
  // Set up Adminer environment
  $_GET['sqlite'] = BAUM_ORGS_DB_PATH;
  $_GET['username'] = '';
  
  // Custom Adminer class for SQLite
  class AdminerBaumOrgs {
    function name() {
      return 'Baum Organizations Database Manager';
    }
    
    function credentials() {
      return array(BAUM_ORGS_DB_PATH, '', '');
    }
    
    function database() {
      return 'organizations';
    }
    
    function login($login, $password) {
      return true;
    }
    
    function css() {
      return "
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; }
        #menu { background: #0073aa; }
        #menu a { color: white; }
        #menu a:hover { background: rgba(255,255,255,0.1); }
        .logout { background: #d63638; }
        h1 { color: #0073aa; }
        .js .column { background: #f9f9f9; }
        input[type='submit'], input[type='button'], button { 
          background: #0073aa; 
          border-color: #0073aa; 
          color: white; 
        }
        input[type='submit']:hover, input[type='button']:hover, button:hover { 
          background: #005a87; 
          border-color: #005a87; 
        }
      ";
    }
    
    function navigation($missing) {
      global $SELF;
      echo '<h1><a href="' . admin_url('admin.php?page=baum-organizations-db') . '">← ' . __('Back to WordPress', 'baum-organizations') . '</a></h1>';
      echo '<h2>' . $this->name() . '</h2>';
    }
  }
  
  // Include Adminer
  function adminer_object() {
    return new AdminerBaumOrgs;
  }
  
  include BAUM_ORGS_PLUGIN_DIR . 'admin/adminer.php';
  exit;
}

// Show database manager interface
?>
<div class="wrap">
  <h1>
    <?php _e('Database Manager', 'baum-organizations'); ?>
    <a href="<?php echo admin_url('admin.php?page=baum-organizations-db&logout=1'); ?>" class="page-title-action">
      <?php _e('Logout', 'baum-organizations'); ?>
    </a>
  </h1>
  
  <!-- Database Information -->
  <div class="card">
    <h2><?php _e('Database Information', 'baum-organizations'); ?></h2>
    
    <?php
    $db_size = filesize(BAUM_ORGS_DB_PATH);
    $db_modified = filemtime(BAUM_ORGS_DB_PATH);
    
    // Get database statistics
    $db_stats = array();
    $db = BaumOrganizations::get_db_connection();
    if ($db) {
      try {
        $queries = array(
          'total' => "SELECT COUNT(*) as count FROM organizations",
          'with_logos' => "SELECT COUNT(*) as count FROM organizations WHERE logo_filename IS NOT NULL",
          'with_websites' => "SELECT COUNT(*) as count FROM organizations WHERE website IS NOT NULL",
          'with_linkedin' => "SELECT COUNT(*) as count FROM organizations WHERE linkedin_url IS NOT NULL",
          'countries' => "SELECT COUNT(DISTINCT country) as count FROM organizations WHERE country IS NOT NULL",
          'industries' => "SELECT COUNT(DISTINCT industry) as count FROM organizations WHERE industry IS NOT NULL"
        );
        
        foreach ($queries as $key => $query) {
          $stmt = $db->query($query);
          $result = $stmt->fetch(PDO::FETCH_ASSOC);
          $db_stats[$key] = $result['count'];
        }
      } catch (PDOException $e) {
        error_log('Database stats error: ' . $e->getMessage());
      }
    }
    ?>
    
    <table class="form-table">
      <tr>
        <th scope="row"><?php _e('Database File', 'baum-organizations'); ?></th>
        <td><code><?php echo esc_html(BAUM_ORGS_DB_PATH); ?></code></td>
      </tr>
      <tr>
        <th scope="row"><?php _e('File Size', 'baum-organizations'); ?></th>
        <td><?php echo size_format($db_size); ?></td>
      </tr>
      <tr>
        <th scope="row"><?php _e('Last Modified', 'baum-organizations'); ?></th>
        <td><?php echo date_i18n(get_option('date_format') . ' ' . get_option('time_format'), $db_modified); ?></td>
      </tr>
      <?php if (!empty($db_stats)): ?>
        <tr>
          <th scope="row"><?php _e('Total Organizations', 'baum-organizations'); ?></th>
          <td><?php echo number_format($db_stats['total']); ?></td>
        </tr>
        <tr>
          <th scope="row"><?php _e('With Logos', 'baum-organizations'); ?></th>
          <td><?php echo number_format($db_stats['with_logos']); ?> (<?php echo round(($db_stats['with_logos'] / $db_stats['total']) * 100, 1); ?>%)</td>
        </tr>
        <tr>
          <th scope="row"><?php _e('With Websites', 'baum-organizations'); ?></th>
          <td><?php echo number_format($db_stats['with_websites']); ?> (<?php echo round(($db_stats['with_websites'] / $db_stats['total']) * 100, 1); ?>%)</td>
        </tr>
        <tr>
          <th scope="row"><?php _e('With LinkedIn', 'baum-organizations'); ?></th>
          <td><?php echo number_format($db_stats['with_linkedin']); ?> (<?php echo round(($db_stats['with_linkedin'] / $db_stats['total']) * 100, 1); ?>%)</td>
        </tr>
        <tr>
          <th scope="row"><?php _e('Unique Countries', 'baum-organizations'); ?></th>
          <td><?php echo number_format($db_stats['countries']); ?></td>
        </tr>
        <tr>
          <th scope="row"><?php _e('Unique Industries', 'baum-organizations'); ?></th>
          <td><?php echo number_format($db_stats['industries']); ?></td>
        </tr>
      <?php endif; ?>
    </table>
  </div>
  
  <!-- Access Adminer -->
  <div class="card">
    <h2><?php _e('Database Administration', 'baum-organizations'); ?></h2>
    <p><?php _e('Use Adminer to browse, query, and manage the SQLite database directly.', 'baum-organizations'); ?></p>
    
    <p>
      <a href="<?php echo admin_url('admin.php?page=baum-organizations-db&adminer=1'); ?>" 
         class="button button-primary" target="_blank">
        <?php _e('Open Adminer', 'baum-organizations'); ?>
      </a>
    </p>
    
    <div class="notice notice-info inline">
      <p>
        <strong><?php _e('Note:', 'baum-organizations'); ?></strong>
        <?php _e('Adminer will open in a new tab. You can browse tables, run SQL queries, export data, and more.', 'baum-organizations'); ?>
      </p>
    </div>
  </div>
  
  <!-- Quick Actions -->
  <div class="card">
    <h2><?php _e('Quick Actions', 'baum-organizations'); ?></h2>
    
    <p>
      <a href="<?php echo admin_url('admin.php?page=baum-organizations'); ?>" class="button">
        <?php _e('Browse Organizations', 'baum-organizations'); ?>
      </a>
      
      <a href="<?php echo admin_url('admin.php?page=baum-organizations-import'); ?>" class="button">
        <?php _e('Import Data', 'baum-organizations'); ?>
      </a>
    </p>
  </div>
  
  <!-- Security Notice -->
  <div class="card">
    <h2><?php _e('Security Information', 'baum-organizations'); ?></h2>
    
    <div class="notice notice-warning inline">
      <p>
        <strong><?php _e('Important Security Notes:', 'baum-organizations'); ?></strong>
      </p>
      <ul>
        <li><?php _e('Change the default database manager credentials in the plugin code', 'baum-organizations'); ?></li>
        <li><?php _e('The database manager is only accessible to WordPress administrators', 'baum-organizations'); ?></li>
        <li><?php _e('Always backup your database before making changes', 'baum-organizations'); ?></li>
        <li><?php _e('Be careful when running SQL queries - they can modify or delete data', 'baum-organizations'); ?></li>
      </ul>
    </div>
    
    <h3><?php _e('Current Credentials', 'baum-organizations'); ?></h3>
    <p>
      <strong><?php _e('Username:', 'baum-organizations'); ?></strong> <code><?php echo esc_html(BAUM_ORGS_DB_USER); ?></code><br>
      <strong><?php _e('Password:', 'baum-organizations'); ?></strong> <code><?php echo esc_html(BAUM_ORGS_DB_PASS); ?></code>
    </p>
    
    <p>
      <small>
        <?php _e('To change these credentials, edit the BAUM_ORGS_DB_USER and BAUM_ORGS_DB_PASS constants in', 'baum-organizations'); ?>
        <code>admin/database-manager.php</code>
      </small>
    </p>
  </div>
</div>

<style>
.card {
  background: #fff;
  border: 1px solid #ccd0d4;
  border-radius: 4px;
  padding: 20px;
  margin: 20px 0;
  box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.card h2 {
  margin-top: 0;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.notice.inline {
  margin: 15px 0;
  padding: 12px;
}

.form-table th {
  width: 200px;
}
</style>

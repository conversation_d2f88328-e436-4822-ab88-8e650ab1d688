<?php
/**
 * Main admin page for Baum Organizations
 */

// Prevent direct access
if (!defined('ABSPATH')) {
  exit;
}

// Include utilities
require_once BAUM_ORGS_PLUGIN_DIR . 'includes/class-utils.php';

// Handle search
$search_query = isset($_GET['search']) ? sanitize_text_field($_GET['search']) : '';
$page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
$per_page = 20;
$offset = ($page - 1) * $per_page;

// Get organizations
$organizations = BaumOrganizations::search_organizations($search_query, $per_page, $offset);

// Get total count for pagination
$total_count = BaumOrganizations::get_organization_count($search_query);

$total_pages = ceil($total_count / $per_page);
?>

<div class="wrap">
  <h1><?php _e('Organizations', 'baum-organizations'); ?></h1>
  
  <?php
  // Check if table exists and has data
  global $wpdb;
  $table_name = $wpdb->prefix . 'baum_organizations';
  $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
  $has_data = $table_exists ? $wpdb->get_var("SELECT COUNT(*) FROM $table_name") > 0 : false;
  ?>

  <?php if (!$table_exists || !$has_data): ?>
    <div class="notice notice-warning">
      <p>
        <?php _e('No organization data found. Please import the organization data first.', 'baum-organizations'); ?>
        <a href="<?php echo admin_url('admin.php?page=baum-organizations-import'); ?>" class="button">
          <?php _e('Import Data', 'baum-organizations'); ?>
        </a>
      </p>
    </div>
  <?php else: ?>

    <!-- Statistics Dashboard -->
    <div class="baum-stats-dashboard" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;">
      <div class="stat-card">
        <h3><?php echo number_format($wpdb->get_var("SELECT COUNT(*) FROM $table_name")); ?></h3>
        <p><?php _e('Total Organizations', 'baum-organizations'); ?></p>
      </div>

      <div class="stat-card">
        <h3><?php echo number_format($wpdb->get_var("SELECT COUNT(*) FROM $table_name WHERE logo_filename IS NOT NULL")); ?></h3>
        <p><?php _e('With Logos', 'baum-organizations'); ?></p>
      </div>

      <div class="stat-card">
        <h3><?php echo number_format($wpdb->get_var("SELECT COUNT(*) FROM $table_name WHERE website IS NOT NULL")); ?></h3>
        <p><?php _e('With Websites', 'baum-organizations'); ?></p>
      </div>

      <div class="stat-card">
        <h3><?php echo number_format($wpdb->get_var("SELECT COUNT(*) FROM $table_name WHERE linkedin_data IS NOT NULL")); ?></h3>
        <p><?php _e('LinkedIn Enriched', 'baum-organizations'); ?></p>
      </div>

      <div class="stat-card">
        <h3><?php echo number_format($wpdb->get_var("SELECT COUNT(DISTINCT industry) FROM $table_name WHERE industry IS NOT NULL")); ?></h3>
        <p><?php _e('Industries', 'baum-organizations'); ?></p>
      </div>

      <div class="stat-card">
        <h3><?php echo number_format($wpdb->get_var("SELECT COUNT(DISTINCT country) FROM $table_name WHERE country IS NOT NULL")); ?></h3>
        <p><?php _e('Countries', 'baum-organizations'); ?></p>
      </div>
    </div>
    
    <!-- Search Form -->
    <div class="baum-orgs-search-form">
      <form method="get" action="">
        <input type="hidden" name="page" value="baum-organizations">
        <p class="search-box">
          <label class="screen-reader-text" for="organization-search-input">
            <?php _e('Search Organizations:', 'baum-organizations'); ?>
          </label>
          <input type="search" id="organization-search-input" name="search" 
                 value="<?php echo esc_attr($search_query); ?>" 
                 placeholder="<?php _e('Search organizations...', 'baum-organizations'); ?>">
          <input type="submit" id="search-submit" class="button" 
                 value="<?php _e('Search', 'baum-organizations'); ?>">
        </p>
      </form>
    </div>
    
    <!-- Results Summary -->
    <div class="baum-orgs-results-summary">
      <p>
        <?php
        if (!empty($search_query)) {
          printf(
            __('Showing %d results for "%s" (Page %d of %d)', 'baum-organizations'),
            count($organizations),
            esc_html($search_query),
            $page,
            $total_pages
          );
        } else {
          printf(
            __('Showing %d organizations (Page %d of %d)', 'baum-organizations'),
            count($organizations),
            $page,
            $total_pages
          );
        }
        ?>
      </p>
    </div>
    
    <!-- Organizations Table -->
    <?php if (!empty($organizations)): ?>
      <table class="wp-list-table widefat fixed striped">
        <thead>
          <tr>
            <th scope="col" class="column-logo"><?php _e('Logo', 'baum-organizations'); ?></th>
            <th scope="col" class="column-name"><?php _e('Name', 'baum-organizations'); ?></th>
            <th scope="col" class="column-website"><?php _e('Website', 'baum-organizations'); ?></th>
            <th scope="col" class="column-industry"><?php _e('Industry', 'baum-organizations'); ?></th>
            <th scope="col" class="column-size"><?php _e('Size', 'baum-organizations'); ?></th>
            <th scope="col" class="column-location"><?php _e('Location', 'baum-organizations'); ?></th>
            <th scope="col" class="column-linkedin"><?php _e('LinkedIn', 'baum-organizations'); ?></th>
            <th scope="col" class="column-founded"><?php _e('Founded', 'baum-organizations'); ?></th>
          </tr>
        </thead>
        <tbody>
          <?php foreach ($organizations as $org): ?>
            <tr>
              <td class="column-logo">
                <?php
                $logo_filename = BaumOrganizations_Utils::get_logo_filename($org['name']);
                if ($logo_filename):
                  $logo_url = BAUM_ORGS_LOGOS_URL . $logo_filename;
                ?>
                  <img src="<?php echo esc_url($logo_url); ?>" 
                       alt="<?php echo esc_attr($org['name']); ?>" 
                       class="org-logo" 
                       width="32" height="32">
                <?php else: ?>
                  <div class="org-logo-placeholder">
                    <span class="dashicons dashicons-building"></span>
                  </div>
                <?php endif; ?>
              </td>
              
              <td class="column-name">
                <strong><?php echo esc_html($org['name']); ?></strong>
                <?php if (!empty($org['linkedin_url'])): ?>
                  <br>
                  <a href="https://<?php echo esc_attr($org['linkedin_url']); ?>" 
                     target="_blank" class="linkedin-link">
                    <span class="dashicons dashicons-linkedin"></span>
                    <?php _e('LinkedIn', 'baum-organizations'); ?>
                  </a>
                <?php endif; ?>
              </td>
              
              <td class="column-website">
                <?php if (!empty($org['website'])): ?>
                  <a href="https://<?php echo esc_attr($org['website']); ?>" 
                     target="_blank" rel="noopener">
                    <?php echo esc_html($org['website']); ?>
                    <span class="dashicons dashicons-external"></span>
                  </a>
                <?php else: ?>
                  <span class="text-muted"><?php _e('N/A', 'baum-organizations'); ?></span>
                <?php endif; ?>
              </td>
              
              <td class="column-industry">
                <?php echo !empty($org['industry']) ? esc_html(ucwords($org['industry'])) : '<span class="text-muted">' . __('N/A', 'baum-organizations') . '</span>'; ?>
              </td>
              
              <td class="column-size">
                <?php echo esc_html(BaumOrganizations_Utils::format_company_size($org['size'])); ?>
              </td>
              
              <td class="column-location">
                <?php
                $location_parts = array();
                if (!empty($org['locality'])) {
                  $location_parts[] = ucwords($org['locality']);
                }
                if (!empty($org['region'])) {
                  $location_parts[] = ucwords($org['region']);
                }
                if (!empty($org['country'])) {
                  $country_flag = BaumOrganizations_Utils::get_country_flag($org['country']);
                  $location_parts[] = $country_flag . ' ' . ucwords($org['country']);
                }

                echo !empty($location_parts) ? esc_html(implode(', ', $location_parts)) : '<span class="text-muted">' . __('N/A', 'baum-organizations') . '</span>';
                ?>
              </td>

              <td class="column-linkedin">
                <?php if (!empty($org['linkedin_data'])): ?>
                  <span class="dashicons dashicons-yes-alt" style="color: #46b450;" title="<?php _e('LinkedIn data available', 'baum-organizations'); ?>"></span>
                  <?php _e('Enriched', 'baum-organizations'); ?>
                <?php else: ?>
                  <span class="text-muted"><?php _e('Not enriched', 'baum-organizations'); ?></span>
                <?php endif; ?>
              </td>

              <td class="column-founded">
                <?php echo !empty($org['founded']) ? esc_html($org['founded']) : '<span class="text-muted">' . __('N/A', 'baum-organizations') . '</span>'; ?>
              </td>
            </tr>
          <?php endforeach; ?>
        </tbody>
      </table>
      
      <!-- Pagination -->
      <?php if ($total_pages > 1): ?>
        <div class="tablenav bottom">
          <div class="tablenav-pages">
            <?php
            $pagination_args = array(
              'base' => add_query_arg('paged', '%#%'),
              'format' => '',
              'prev_text' => __('&laquo; Previous', 'baum-organizations'),
              'next_text' => __('Next &raquo;', 'baum-organizations'),
              'total' => $total_pages,
              'current' => $page,
              'add_args' => array('search' => $search_query)
            );
            
            echo paginate_links($pagination_args);
            ?>
          </div>
        </div>
      <?php endif; ?>
      
    <?php else: ?>
      <div class="notice notice-info">
        <p>
          <?php
          if (!empty($search_query)) {
            printf(__('No organizations found matching "%s".', 'baum-organizations'), esc_html($search_query));
          } else {
            _e('No organizations found.', 'baum-organizations');
          }
          ?>
        </p>
      </div>
    <?php endif; ?>
    
  <?php endif; ?>
</div>

<style>
.baum-stats-dashboard {
  margin: 20px 0;
}

.stat-card {
  background: white;
  border: 1px solid #ccd0d4;
  border-radius: 4px;
  padding: 20px;
  text-align: center;
  box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.stat-card h3 {
  margin: 0 0 10px 0;
  font-size: 2em;
  color: #0073aa;
  font-weight: 600;
}

.stat-card p {
  margin: 0;
  color: #666;
  font-size: 14px;
  font-weight: 500;
}

.baum-orgs-search-form {
  margin: 20px 0;
}

.baum-orgs-results-summary {
  margin: 10px 0;
  font-style: italic;
  color: #666;
}

.org-logo {
  border-radius: 4px;
  border: 1px solid #ddd;
}

.org-logo-placeholder {
  width: 32px;
  height: 32px;
  background: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
}

.linkedin-link {
  font-size: 12px;
  color: #0077b5;
  text-decoration: none;
}

.linkedin-link:hover {
  text-decoration: underline;
}

.text-muted {
  color: #999;
  font-style: italic;
}

.column-logo {
  width: 50px;
}

.column-name {
  width: 200px;
}

.column-website {
  width: 150px;
}

.column-industry {
  width: 150px;
}

.column-size {
  width: 120px;
}

.column-location {
  width: 200px;
}

.column-founded {
  width: 80px;
}
</style>

# Baum Organizations WordPress Plugin

A comprehensive WordPress plugin for managing company/organization data with SQLite database integration and logo management.

## Features

- **SQLite Database Integration**: Converts large JSON datasets into efficient SQLite database
- **Logo Matching**: Automatically matches organization names with logo files
- **Search & Browse**: Powerful search functionality with pagination
- **Frontend Shortcodes**: Display companies on pages/posts with `[baum_company]` and more
- **Database Manager**: Secure Adminer integration for database administration
- **WordPress Integration**: Native WordPress admin interface
- **Responsive Design**: Mobile-friendly admin interface
- **Security**: Authentication-protected database access

## Installation

1. **Download/Clone** the plugin to your WordPress plugins directory:
   ```bash
   cd /path/to/wordpress/wp-content/plugins/
   git clone https://github.com/yourusername/baum-organizations.git
   ```

2. **Place Required Files**:
   - Copy `company-dataset.json` to the plugin root directory
   - Ensure the `logos/` directory contains SVG logo files

3. **Activate Plugin**:
   - Go to WordPress Admin → Plugins
   - Activate "Baum Organizations"

## Setup Instructions

### 1. Import Organization Data

1. Navigate to **Organizations → Import Data** in WordPress admin
2. Verify system requirements:
   - JSON dataset file present
   - Logos directory available
   - Sufficient PHP memory (2GB+ recommended)
   - Adequate execution time (300s+ or unlimited)
3. Click **"Start Import"** to begin conversion process

**Alternative: Command Line Import**
```bash
cd /path/to/plugin/directory
php scripts/convert-dataset.php
```

### 2. Database Manager Access

**Default Credentials:**
- **Username**: `admin`
- **Password**: `BaumOrgs2024!`

**⚠️ IMPORTANT**: Change these credentials immediately for security!

**To Change Credentials:**
1. Edit `admin/database-manager.php`
2. Modify these constants:
   ```php
   define('BAUM_ORGS_DB_USER', 'your_username');
   define('BAUM_ORGS_DB_PASS', 'your_secure_password');
   ```

### 3. Access Database Manager

1. Go to **Organizations → Database Manager**
2. Login with your credentials
3. Click **"Open Adminer"** to access the database interface

## File Structure

```
baum-organizations/
├── baum-organizations.php      # Main plugin file
├── README.md                   # This file
├── LICENSE                     # License file
├── company-dataset.json        # Your dataset (place here)
├── logos/                      # Logo files directory
│   ├── company1.svg
│   ├── company2.svg
│   └── ...
├── admin/                      # Admin interface files
│   ├── admin-page.php         # Main organizations page
│   ├── database-manager.php   # Database manager interface
│   ├── import-page.php        # Data import interface
│   └── adminer.php            # Adminer database tool
├── assets/                     # CSS and JavaScript files
│   ├── css/
│   │   └── admin.css          # Admin styles
│   └── js/
│       └── admin.js           # Admin JavaScript
├── database/                   # SQLite database storage
│   └── organizations.db       # Generated database
├── includes/                   # PHP classes and utilities
│   └── class-utils.php        # Utility functions
└── scripts/                    # Conversion scripts
    └── convert-dataset.php     # Dataset conversion script
```

## Database Schema

The SQLite database contains an `organizations` table with the following structure:

| Column | Type | Description |
|--------|------|-------------|
| id | TEXT | Unique organization identifier |
| website | TEXT | Organization website |
| name | TEXT | Organization name (NOT NULL) |
| founded | INTEGER | Year founded |
| size | TEXT | Company size (e.g., "1-10", "51-200") |
| locality | TEXT | City/locality |
| region | TEXT | State/region |
| country | TEXT | Country |
| industry | TEXT | Industry category |
| linkedin_url | TEXT | LinkedIn profile URL |
| logo_filename | TEXT | Matched logo filename |
| search_index | TEXT | Full-text search index |
| created_at | DATETIME | Record creation timestamp |
| updated_at | DATETIME | Record update timestamp |

## Logo Matching Algorithm

The plugin automatically matches organization names with logo files using:

1. **Name Normalization**: Removes common business suffixes (Inc, Ltd, Corp, etc.)
2. **Character Cleaning**: Removes special characters, normalizes spacing
3. **Variation Generation**: Creates multiple name variations to try
4. **File Matching**: Searches for corresponding `.svg` files in logos directory

**Example Matching:**
- "Apple Inc." → `apple.svg`
- "Microsoft Corporation" → `microsoft.svg`
- "AT&T" → `atandt.svg`

## Usage Examples

### Shortcodes (Frontend Display)
Display companies on your WordPress pages and posts:

```
<!-- Display a single company profile -->
[baum_company name="Apple Inc."]

<!-- Show a list of technology companies -->
[baum_company_list industry="technology" limit="5"]

<!-- Add a company search form -->
[baum_company_search]
```

See [SHORTCODES.md](SHORTCODES.md) for complete shortcode documentation.

### PHP Functions
```php
// Search for organizations
$results = baum_orgs_search('technology', 20, 0);

// Get organization logo URL
$logo_url = baum_orgs_get_logo_url('Apple Inc.');
```

### Custom Queries
Access the database directly for custom queries:
```php
$db = BaumOrganizations::get_db_connection();
$stmt = $db->prepare("SELECT * FROM organizations WHERE country = ? LIMIT 10");
$stmt->execute(['united states']);
$results = $stmt->fetchAll(PDO::FETCH_ASSOC);
```

## Security Considerations

1. **Change Default Credentials**: Immediately change database manager credentials
2. **File Permissions**: Ensure proper file permissions on database directory
3. **Access Control**: Database manager is restricted to WordPress administrators
4. **Backup**: Regular database backups recommended
5. **Updates**: Keep plugin updated for security patches

## Troubleshooting

### Import Issues

**Memory Errors:**
```bash
# Increase PHP memory limit
ini_set('memory_limit', '2G');
```

**Timeout Errors:**
```bash
# Increase execution time
ini_set('max_execution_time', 0);
```

**Permission Errors:**
```bash
# Fix directory permissions
chmod 755 /path/to/plugin/database/
chmod 644 /path/to/plugin/database/organizations.db
```

### Database Issues

**Connection Errors:**
- Verify SQLite extension is installed: `php -m | grep sqlite`
- Check database file permissions
- Ensure database directory is writable

**Performance Issues:**
- Database includes indexes for common queries
- Consider pagination for large result sets
- Use specific search terms rather than broad queries

### Logo Issues

**Missing Logos:**
- Verify logo files are in SVG format
- Check filename matches expected pattern
- Use database manager to verify `logo_filename` column

## Development

### Requirements
- PHP 7.4+
- WordPress 5.0+
- SQLite3 extension
- PDO SQLite driver

### Testing
```bash
# Run conversion script with test data
php scripts/convert-dataset.php

# Check database integrity
sqlite3 database/organizations.db "PRAGMA integrity_check;"
```

## API Reference

### Main Functions

**`baum_orgs_search($query, $limit, $offset)`**
- Search organizations with pagination
- Returns array of organization data

**`baum_orgs_get_logo_url($organization_name)`**
- Get logo URL for organization
- Returns URL string or false if not found

### Classes

**`BaumOrganizations`**
- Main plugin class
- Handles WordPress integration

**`BaumOrganizations_Utils`**
- Utility functions for logo matching
- Data sanitization and formatting

## Contributing

1. Fork the repository
2. Create feature branch
3. Make changes with proper documentation
4. Test thoroughly
5. Submit pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue on GitHub
- Check troubleshooting section above
- Review WordPress debug logs

## Changelog

### Version 1.0.0
- Initial release
- SQLite database integration
- Logo matching system
- Adminer database manager
- WordPress admin interface
- Search and pagination
- Security features

---

**Note**: This plugin is designed for large datasets and may require server resources adjustment for optimal performance.

<?php
/**
 * Plugin Name: Baum Organizations
 * Plugin URI: https://github.com/tb<PERSON><PERSON>/baum-organizations
 * Description: A WordPress plugin for managing company/organization data with SQLite database and logo management.
 * Version: 1.0.0
 * Author: <PERSON>
 * License: MIT
 * Text Domain: baum-organizations
 * Domain Path: /languages
 */

// Prevent direct access
if (!defined('ABSPATH')) {
  exit;
}

// Define plugin constants
define('BAUM_ORGS_VERSION', '2.0.0');
define('BAUM_ORGS_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('BAUM_ORGS_PLUGIN_URL', plugin_dir_url(__FILE__));
define('BAUM_ORGS_LOGOS_DIR', BAUM_ORGS_PLUGIN_DIR . 'logos/');
define('BAUM_ORGS_LOGOS_URL', BAUM_ORGS_PLUGIN_URL . 'logos/');

/**
 * Main plugin class
 */
class BaumOrganizations {
  
  /**
   * Constructor
   */
  public function __construct() {
    add_action('init', array($this, 'init'));
    add_action('admin_menu', array($this, 'add_admin_menu'));
    add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
    add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
    
    // Register activation and deactivation hooks
    register_activation_hook(__FILE__, array($this, 'activate'));
    register_deactivation_hook(__FILE__, array($this, 'deactivate'));
  }
  
  /**
   * Initialize plugin
   */
  public function init() {
    // Load text domain for translations
    load_plugin_textdomain('baum-organizations', false, dirname(plugin_basename(__FILE__)) . '/languages');

    // Create logos directory if it doesn't exist
    if (!file_exists(BAUM_ORGS_LOGOS_DIR)) {
      wp_mkdir_p(BAUM_ORGS_LOGOS_DIR);
    }
  }
  
  /**
   * Add admin menu
   */
  public function add_admin_menu() {
    add_menu_page(
      __('Organizations', 'baum-organizations'),
      __('Organizations', 'baum-organizations'),
      'manage_options',
      'baum-organizations',
      array($this, 'admin_page'),
      'dashicons-building',
      30
    );
    
    add_submenu_page(
      'baum-organizations',
      __('LinkedIn Enrichment', 'baum-organizations'),
      __('LinkedIn Enrichment', 'baum-organizations'),
      'manage_options',
      'baum-organizations-linkedin',
      array($this, 'linkedin_page')
    );

    add_submenu_page(
      'baum-organizations',
      __('Import Data', 'baum-organizations'),
      __('Import Data', 'baum-organizations'),
      'manage_options',
      'baum-organizations-import',
      array($this, 'import_page')
    );
  }
  
  /**
   * Main admin page
   */
  public function admin_page() {
    include BAUM_ORGS_PLUGIN_DIR . 'admin/admin-page.php';
  }
  
  /**
   * LinkedIn enrichment page
   */
  public function linkedin_page() {
    include BAUM_ORGS_PLUGIN_DIR . 'admin/linkedin-page.php';
  }

  /**
   * Import page
   */
  public function import_page() {
    include BAUM_ORGS_PLUGIN_DIR . 'admin/import-page.php';
  }
  
  /**
   * Enqueue frontend scripts and styles
   */
  public function enqueue_scripts() {
    wp_enqueue_style(
      'baum-organizations-style',
      BAUM_ORGS_PLUGIN_URL . 'assets/css/style.css',
      array(),
      BAUM_ORGS_VERSION
    );
    
    wp_enqueue_script(
      'baum-organizations-script',
      BAUM_ORGS_PLUGIN_URL . 'assets/js/script.js',
      array('jquery'),
      BAUM_ORGS_VERSION,
      true
    );
  }
  
  /**
   * Enqueue admin scripts and styles
   */
  public function enqueue_admin_scripts($hook) {
    if (strpos($hook, 'baum-organizations') === false) {
      return;
    }
    
    wp_enqueue_style(
      'baum-organizations-admin-style',
      BAUM_ORGS_PLUGIN_URL . 'assets/css/admin.css',
      array(),
      BAUM_ORGS_VERSION
    );
    
    wp_enqueue_script(
      'baum-organizations-admin-script',
      BAUM_ORGS_PLUGIN_URL . 'assets/js/admin.js',
      array('jquery'),
      BAUM_ORGS_VERSION,
      true
    );
  }
  
  /**
   * Plugin activation
   */
  public function activate() {
    // Create database table
    $this->create_database_table();

    // Create necessary directories
    $directories = array(
      BAUM_ORGS_LOGOS_DIR,
      BAUM_ORGS_PLUGIN_DIR . 'admin',
      BAUM_ORGS_PLUGIN_DIR . 'assets/css',
      BAUM_ORGS_PLUGIN_DIR . 'assets/js',
      BAUM_ORGS_PLUGIN_DIR . 'includes'
    );

    foreach ($directories as $dir) {
      if (!file_exists($dir)) {
        wp_mkdir_p($dir);
      }
    }

    // Set default options
    add_option('baum_orgs_version', BAUM_ORGS_VERSION);
    add_option('baum_orgs_db_imported', false);
    add_option('baum_orgs_linkedin_api_key', '');
  }
  
  /**
   * Plugin deactivation
   */
  public function deactivate() {
    // Clean up if needed
  }

  /**
   * Create database table in WordPress MySQL
   */
  private function create_database_table() {
    global $wpdb;

    $table_name = $wpdb->prefix . 'baum_organizations';

    $charset_collate = $wpdb->get_charset_collate();

    $sql = "CREATE TABLE $table_name (
      id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
      name varchar(255) NOT NULL,
      website varchar(255) DEFAULT NULL,
      industry varchar(100) DEFAULT NULL,
      size varchar(50) DEFAULT NULL,
      founded year DEFAULT NULL,
      locality varchar(100) DEFAULT NULL,
      region varchar(100) DEFAULT NULL,
      country varchar(100) DEFAULT NULL,
      linkedin_url varchar(500) DEFAULT NULL,
      linkedin_data longtext DEFAULT NULL,
      logo_filename varchar(255) DEFAULT NULL,
      enriched_at datetime DEFAULT NULL,
      created_at datetime DEFAULT CURRENT_TIMESTAMP,
      updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (id),
      KEY idx_name (name),
      KEY idx_industry (industry),
      KEY idx_country (country),
      KEY idx_website (website),
      KEY idx_linkedin (linkedin_url),
      UNIQUE KEY unique_name_website (name, website)
    ) $charset_collate;";

    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
  }

  /**
   * Get organization by name
   */
  public static function get_organization($name) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'baum_organizations';

    return $wpdb->get_row($wpdb->prepare(
      "SELECT * FROM $table_name WHERE name = %s LIMIT 1",
      $name
    ), ARRAY_A);
  }

  /**
   * Search organizations
   */
  public static function search_organizations($query = '', $limit = 20, $offset = 0) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'baum_organizations';

    if (!empty($query)) {
      return $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM $table_name
         WHERE name LIKE %s OR website LIKE %s OR industry LIKE %s
         ORDER BY name ASC LIMIT %d OFFSET %d",
        '%' . $wpdb->esc_like($query) . '%',
        '%' . $wpdb->esc_like($query) . '%',
        '%' . $wpdb->esc_like($query) . '%',
        $limit,
        $offset
      ), ARRAY_A);
    } else {
      return $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM $table_name ORDER BY name ASC LIMIT %d OFFSET %d",
        $limit,
        $offset
      ), ARRAY_A);
    }
  }

  /**
   * Get total organization count
   */
  public static function get_organization_count($query = '') {
    global $wpdb;
    $table_name = $wpdb->prefix . 'baum_organizations';

    if (!empty($query)) {
      return $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM $table_name
         WHERE name LIKE %s OR website LIKE %s OR industry LIKE %s",
        '%' . $wpdb->esc_like($query) . '%',
        '%' . $wpdb->esc_like($query) . '%',
        '%' . $wpdb->esc_like($query) . '%'
      ));
    } else {
      return $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
    }
  }

  /**
   * Insert or update organization
   */
  public static function save_organization($data) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'baum_organizations';

    // Check if organization exists
    $existing = $wpdb->get_row($wpdb->prepare(
      "SELECT id FROM $table_name WHERE name = %s AND website = %s LIMIT 1",
      $data['name'],
      $data['website']
    ));

    if ($existing) {
      // Update existing
      return $wpdb->update(
        $table_name,
        $data,
        array('id' => $existing->id)
      );
    } else {
      // Insert new
      return $wpdb->insert($table_name, $data);
    }
  }
}

// Include required files
require_once BAUM_ORGS_PLUGIN_DIR . 'includes/class-utils.php';
require_once BAUM_ORGS_PLUGIN_DIR . 'includes/class-shortcodes.php';

// Initialize the plugin
new BaumOrganizations();

// Initialize shortcodes
new BaumOrganizations_Shortcodes();

/**
 * Helper functions
 */

/**
 * Get organization logo URL
 */
function baum_orgs_get_logo_url($organization_name) {
  $logo_filename = BaumOrganizations_Utils::get_logo_filename($organization_name);
  if ($logo_filename) {
    return BAUM_ORGS_LOGOS_URL . $logo_filename;
  }
  return false;
}

/**
 * Search organizations (public function)
 */
function baum_orgs_search($query = '', $limit = 20, $offset = 0) {
  return BaumOrganizations::search_organizations($query, $limit, $offset);
}

<?php
/**
 * Plugin Name: Baum Organizations
 * Plugin URI: https://github.com/tb<PERSON><PERSON>/baum-organizations
 * Description: A WordPress plugin for managing company/organization data with SQLite database and logo management.
 * Version: 1.0.0
 * Author: <PERSON>
 * License: MIT
 * Text Domain: baum-organizations
 * Domain Path: /languages
 */

// Prevent direct access
if (!defined('ABSPATH')) {
  exit;
}

// Define plugin constants
define('BAUM_ORGS_VERSION', '1.0.0');
define('BAUM_ORGS_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('BAUM_ORGS_PLUGIN_URL', plugin_dir_url(__FILE__));
define('BAUM_ORGS_DB_PATH', BAUM_ORGS_PLUGIN_DIR . 'database/organizations.db');
define('BAUM_ORGS_LOGOS_DIR', BAUM_ORGS_PLUGIN_DIR . 'logos/');
define('BAUM_ORGS_LOGOS_URL', BAUM_ORGS_PLUGIN_URL . 'logos/');

/**
 * Main plugin class
 */
class BaumOrganizations {
  
  /**
   * Constructor
   */
  public function __construct() {
    add_action('init', array($this, 'init'));
    add_action('admin_menu', array($this, 'add_admin_menu'));
    add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
    add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
    
    // Register activation and deactivation hooks
    register_activation_hook(__FILE__, array($this, 'activate'));
    register_deactivation_hook(__FILE__, array($this, 'deactivate'));
  }
  
  /**
   * Initialize plugin
   */
  public function init() {
    // Load text domain for translations
    load_plugin_textdomain('baum-organizations', false, dirname(plugin_basename(__FILE__)) . '/languages');
    
    // Create database directory if it doesn't exist
    $db_dir = dirname(BAUM_ORGS_DB_PATH);
    if (!file_exists($db_dir)) {
      wp_mkdir_p($db_dir);
    }
  }
  
  /**
   * Add admin menu
   */
  public function add_admin_menu() {
    add_menu_page(
      __('Organizations', 'baum-organizations'),
      __('Organizations', 'baum-organizations'),
      'manage_options',
      'baum-organizations',
      array($this, 'admin_page'),
      'dashicons-building',
      30
    );
    
    add_submenu_page(
      'baum-organizations',
      __('Database Manager', 'baum-organizations'),
      __('Database Manager', 'baum-organizations'),
      'manage_options',
      'baum-organizations-db',
      array($this, 'database_manager_page')
    );
    
    add_submenu_page(
      'baum-organizations',
      __('Import Data', 'baum-organizations'),
      __('Import Data', 'baum-organizations'),
      'manage_options',
      'baum-organizations-import',
      array($this, 'import_page')
    );
  }
  
  /**
   * Main admin page
   */
  public function admin_page() {
    include BAUM_ORGS_PLUGIN_DIR . 'admin/admin-page.php';
  }
  
  /**
   * Database manager page
   */
  public function database_manager_page() {
    include BAUM_ORGS_PLUGIN_DIR . 'admin/database-manager.php';
  }
  
  /**
   * Import page
   */
  public function import_page() {
    include BAUM_ORGS_PLUGIN_DIR . 'admin/import-page.php';
  }
  
  /**
   * Enqueue frontend scripts and styles
   */
  public function enqueue_scripts() {
    wp_enqueue_style(
      'baum-organizations-style',
      BAUM_ORGS_PLUGIN_URL . 'assets/css/style.css',
      array(),
      BAUM_ORGS_VERSION
    );
    
    wp_enqueue_script(
      'baum-organizations-script',
      BAUM_ORGS_PLUGIN_URL . 'assets/js/script.js',
      array('jquery'),
      BAUM_ORGS_VERSION,
      true
    );
  }
  
  /**
   * Enqueue admin scripts and styles
   */
  public function enqueue_admin_scripts($hook) {
    if (strpos($hook, 'baum-organizations') === false) {
      return;
    }
    
    wp_enqueue_style(
      'baum-organizations-admin-style',
      BAUM_ORGS_PLUGIN_URL . 'assets/css/admin.css',
      array(),
      BAUM_ORGS_VERSION
    );
    
    wp_enqueue_script(
      'baum-organizations-admin-script',
      BAUM_ORGS_PLUGIN_URL . 'assets/js/admin.js',
      array('jquery'),
      BAUM_ORGS_VERSION,
      true
    );
  }
  
  /**
   * Plugin activation
   */
  public function activate() {
    // Create necessary directories
    $directories = array(
      BAUM_ORGS_PLUGIN_DIR . 'database',
      BAUM_ORGS_PLUGIN_DIR . 'admin',
      BAUM_ORGS_PLUGIN_DIR . 'assets/css',
      BAUM_ORGS_PLUGIN_DIR . 'assets/js',
      BAUM_ORGS_PLUGIN_DIR . 'includes'
    );
    
    foreach ($directories as $dir) {
      if (!file_exists($dir)) {
        wp_mkdir_p($dir);
      }
    }
    
    // Set default options
    add_option('baum_orgs_version', BAUM_ORGS_VERSION);
    add_option('baum_orgs_db_imported', false);
  }
  
  /**
   * Plugin deactivation
   */
  public function deactivate() {
    // Clean up if needed
  }
  
  /**
   * Get SQLite database connection
   */
  public static function get_db_connection() {
    try {
      $pdo = new PDO('sqlite:' . BAUM_ORGS_DB_PATH);
      $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
      return $pdo;
    } catch (PDOException $e) {
      error_log('Baum Organizations DB Error: ' . $e->getMessage());
      return false;
    }
  }
  
  /**
   * Search organizations
   */
  public static function search_organizations($query = '', $limit = 20, $offset = 0) {
    $db = self::get_db_connection();
    if (!$db) {
      return array();
    }
    
    try {
      $sql = "SELECT * FROM organizations";
      $params = array();
      
      if (!empty($query)) {
        $sql .= " WHERE name LIKE :query OR website LIKE :query OR industry LIKE :query";
        $params[':query'] = '%' . $query . '%';
      }
      
      $sql .= " ORDER BY name ASC LIMIT :limit OFFSET :offset";
      $params[':limit'] = $limit;
      $params[':offset'] = $offset;
      
      $stmt = $db->prepare($sql);
      $stmt->execute($params);
      
      return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
      error_log('Baum Organizations Search Error: ' . $e->getMessage());
      return array();
    }
  }
}

// Include required files
require_once BAUM_ORGS_PLUGIN_DIR . 'includes/class-utils.php';

// Initialize the plugin
new BaumOrganizations();

/**
 * Helper functions
 */

/**
 * Get organization logo URL
 */
function baum_orgs_get_logo_url($organization_name) {
  $logo_filename = BaumOrganizations_Utils::get_logo_filename($organization_name);
  if ($logo_filename) {
    return BAUM_ORGS_LOGOS_URL . $logo_filename;
  }
  return false;
}

/**
 * Search organizations (public function)
 */
function baum_orgs_search($query = '', $limit = 20, $offset = 0) {
  return BaumOrganizations::search_organizations($query, $limit, $offset);
}

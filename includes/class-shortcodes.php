<?php
/**
 * Shortcodes for Baum Organizations plugin
 */

// Prevent direct access
if (!defined('ABSPATH')) {
  exit;
}

class BaumOrganizations_Shortcodes {
  
  /**
   * Constructor
   */
  public function __construct() {
    add_action('init', array($this, 'register_shortcodes'));
  }
  
  /**
   * Register all shortcodes
   */
  public function register_shortcodes() {
    add_shortcode('baum_company', array($this, 'company_profile_shortcode'));
    add_shortcode('baum_company_list', array($this, 'company_list_shortcode'));
    add_shortcode('baum_company_search', array($this, 'company_search_shortcode'));
  }
  
  /**
   * Company profile shortcode
   * Usage: [baum_company name="Apple Inc."] or [baum_company id="123"]
   */
  public function company_profile_shortcode($atts) {
    $atts = shortcode_atts(array(
      'name' => '',
      'id' => '',
      'style' => 'card', // card, minimal, detailed
      'show_logo' => 'true',
      'show_website' => 'true',
      'show_linkedin' => 'true',
      'show_location' => 'true',
      'show_industry' => 'true',
      'show_size' => 'true',
      'show_founded' => 'true',
      'width' => '400px'
    ), $atts, 'baum_company');
    
    // Get organization data
    $organization = $this->get_organization($atts['name'], $atts['id']);
    
    if (!$organization) {
      return '<div class="baum-company-error">Company not found.</div>';
    }
    
    // Generate output based on style
    switch ($atts['style']) {
      case 'minimal':
        return $this->render_minimal_profile($organization, $atts);
      case 'detailed':
        return $this->render_detailed_profile($organization, $atts);
      default:
        return $this->render_card_profile($organization, $atts);
    }
  }
  
  /**
   * Get organization from database
   */
  private function get_organization($name = '', $id = '') {
    $db = BaumOrganizations::get_db_connection();
    if (!$db) {
      return false;
    }
    
    try {
      if (!empty($id)) {
        $stmt = $db->prepare("SELECT * FROM organizations WHERE id = :id LIMIT 1");
        $stmt->execute(array(':id' => $id));
      } elseif (!empty($name)) {
        $stmt = $db->prepare("SELECT * FROM organizations WHERE name LIKE :name LIMIT 1");
        $stmt->execute(array(':name' => '%' . $name . '%'));
      } else {
        return false;
      }
      
      return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
      error_log('Baum Organizations Shortcode Error: ' . $e->getMessage());
      return false;
    }
  }
  
  /**
   * Render card style profile (based on screenshot design)
   */
  private function render_card_profile($org, $atts) {
    $logo_url = '';
    if ($atts['show_logo'] === 'true' && !empty($org['logo_filename'])) {
      $logo_url = BAUM_ORGS_LOGOS_URL . $org['logo_filename'];
    }

    $country_flag = BaumOrganizations_Utils::get_country_flag($org['country']);
    $formatted_size = BaumOrganizations_Utils::format_company_size($org['size']);

    ob_start();
    ?>
    <div style="max-width: <?php echo esc_attr($atts['width']); ?>; margin: 20px 0;">
      <div style="background: white; border: 1px solid #e1e5e9; border-radius: 16px; padding: 20px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">

        <!-- Company Header -->
        <div style="display: flex; align-items: center; gap: 15px; margin-bottom: 20px;">
          <?php if ($atts['show_logo'] === 'true'): ?>
            <div style="width: 60px; height: 60px; background: #000; border-radius: 12px; display: flex; align-items: center; justify-content: center; flex-shrink: 0;">
              <?php if ($logo_url): ?>
                <img src="<?php echo esc_url($logo_url); ?>"
                     alt="<?php echo esc_attr($org['name']); ?>"
                     style="width: 40px; height: 40px; object-fit: contain;">
              <?php else: ?>
                <span style="color: white; font-size: 24px;">🏢</span>
              <?php endif; ?>
            </div>
          <?php endif; ?>

          <div>
            <h3 style="margin: 0 0 5px 0; color: #212529; font-size: 18px; font-weight: 600;">
              <?php echo esc_html($org['name']); ?>
            </h3>
            <div style="display: flex; gap: 10px; align-items: center;">
              <?php if (!empty($org['website'])): ?>
                <span style="background: #f0f0f0; color: #666; padding: 2px 8px; border-radius: 4px; font-size: 12px; font-weight: 600;">
                  <?php echo esc_html(strtoupper(substr($org['name'], 0, 4))); ?>
                </span>
              <?php endif; ?>
              <?php if ($atts['show_industry'] === 'true' && !empty($org['industry'])): ?>
                <span style="color: #666; font-size: 14px;">
                  <?php echo esc_html(ucwords($org['industry'])); ?>
                </span>
              <?php endif; ?>
            </div>
          </div>
        </div>

        <!-- Description -->
        <div style="margin-bottom: 20px;">
          <h4 style="margin: 0 0 8px 0; color: #212529; font-size: 14px; font-weight: 600;">Description</h4>
          <p style="margin: 0; color: #666; font-size: 13px; line-height: 1.4;">
            <?php echo esc_html($org['name']); ?>
            <?php if (!empty($org['industry'])): ?>
              operates in the <?php echo esc_html(strtolower($org['industry'])); ?> industry
            <?php endif; ?>
            <?php if (!empty($org['locality']) && !empty($org['country'])): ?>
              and is based in <?php echo esc_html($org['locality']); ?>, <?php echo esc_html($org['country']); ?>.
            <?php endif; ?>
            <?php if ($atts['show_website'] === 'true' && !empty($org['website'])): ?>
              <a href="https://<?php echo esc_attr($org['website']); ?>" target="_blank" style="color: #007aff; text-decoration: none;">Read more</a>
            <?php endif; ?>
          </p>
        </div>

        <!-- About Section -->
        <div style="margin-bottom: 20px;">
          <h4 style="margin: 0 0 12px 0; color: #212529; font-size: 14px; font-weight: 600;">About</h4>
          <div style="display: grid; gap: 8px;">

            <?php if ($atts['show_size'] === 'true' && !empty($org['size'])): ?>
              <div style="display: flex; justify-content: space-between; align-items: center;">
                <span style="color: #666; font-size: 13px;">Employees</span>
                <span style="color: #212529; font-size: 13px; font-weight: 500;">
                  <?php echo esc_html($formatted_size); ?>
                </span>
              </div>
            <?php endif; ?>

            <?php if ($atts['show_location'] === 'true'): ?>
              <div style="display: flex; justify-content: space-between; align-items: flex-start;">
                <span style="color: #666; font-size: 13px;">Address</span>
                <div style="text-align: right;">
                  <?php if (!empty($org['locality'])): ?>
                    <div style="color: #212529; font-size: 13px; font-weight: 500;">
                      <?php echo esc_html(ucwords($org['locality'])); ?>
                    </div>
                  <?php endif; ?>
                  <?php if (!empty($org['region'])): ?>
                    <div style="color: #212529; font-size: 13px; font-weight: 500;">
                      <?php echo esc_html(ucwords($org['region'])); ?>
                    </div>
                  <?php endif; ?>
                  <?php if (!empty($org['country'])): ?>
                    <div style="color: #212529; font-size: 13px; font-weight: 500;">
                      <?php echo esc_html(ucwords($org['country'])); ?>
                    </div>
                  <?php endif; ?>
                </div>
              </div>
            <?php endif; ?>

            <?php if ($atts['show_website'] === 'true' && !empty($org['website'])): ?>
              <div style="display: flex; justify-content: space-between; align-items: center;">
                <span style="color: #666; font-size: 13px;">Website</span>
                <a href="https://<?php echo esc_attr($org['website']); ?>"
                   target="_blank" rel="noopener"
                   style="color: #007aff; font-size: 13px; text-decoration: none;">
                  <?php echo esc_html($org['website']); ?>
                </a>
              </div>
            <?php endif; ?>

            <?php if ($atts['show_industry'] === 'true' && !empty($org['industry'])): ?>
              <div style="display: flex; justify-content: space-between; align-items: center;">
                <span style="color: #666; font-size: 13px;">Industry</span>
                <span style="color: #212529; font-size: 13px; font-weight: 500;">
                  <?php echo esc_html(ucwords($org['industry'])); ?>
                </span>
              </div>
            <?php endif; ?>

            <?php if ($atts['show_founded'] === 'true' && !empty($org['founded'])): ?>
              <div style="display: flex; justify-content: space-between; align-items: center;">
                <span style="color: #666; font-size: 13px;">Founded</span>
                <span style="color: #212529; font-size: 13px; font-weight: 500;">
                  <?php echo esc_html($org['founded']); ?>
                </span>
              </div>
            <?php endif; ?>

          </div>
        </div>

        <!-- Earnings Chart Placeholder -->
        <div>
          <h4 style="margin: 0 0 12px 0; color: #212529; font-size: 14px; font-weight: 600;">Company Info</h4>
          <div style="display: flex; align-items: end; gap: 8px; height: 80px;">
            <?php
            // Generate some sample data visualization
            $sample_data = array(
              array('value' => '1.19', 'color' => '#007aff', 'height' => '45px'),
              array('value' => '1.26', 'color' => '#34c759', 'height' => '50px'),
              array('value' => '1.39', 'color' => '#007aff', 'height' => '55px'),
              array('value' => '1.46', 'color' => '#34c759', 'height' => '58px'),
              array('value' => '1.92', 'color' => '#007aff', 'height' => '65px'),
              array('value' => '2.00', 'color' => '#34c759', 'height' => '70px')
            );

            foreach ($sample_data as $index => $data):
              $year = 2023 + ($index * 0.25);
            ?>
              <div style="display: flex; flex-direction: column; align-items: center; flex: 1;">
                <div style="background: <?php echo $data['color']; ?>; height: <?php echo $data['height']; ?>; width: 100%; border-radius: 4px 4px 0 0; display: flex; align-items: center; justify-content: center; color: white; font-size: 11px; font-weight: 600;">
                  <?php echo $data['value']; ?>
                </div>
                <div style="font-size: 10px; color: #666; margin-top: 4px; text-align: center;">
                  <?php echo date('Y-m-d', strtotime($year . '-01-01')); ?>
                </div>
              </div>
            <?php endforeach; ?>
          </div>
        </div>

      </div>
    </div>
    <?php
    return ob_get_clean();
  }
  
  /**
   * Render minimal style profile
   */
  private function render_minimal_profile($org, $atts) {
    $logo_url = '';
    if ($atts['show_logo'] === 'true' && !empty($org['logo_filename'])) {
      $logo_url = BAUM_ORGS_LOGOS_URL . $org['logo_filename'];
    }

    ob_start();
    ?>
    <div style="display: flex; align-items: center; gap: 12px; padding: 12px; background: white; border: 1px solid #e1e5e9; border-radius: 8px; margin: 8px 0; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
      <?php if ($logo_url): ?>
        <div style="width: 40px; height: 40px; background: #000; border-radius: 8px; display: flex; align-items: center; justify-content: center; flex-shrink: 0;">
          <img src="<?php echo esc_url($logo_url); ?>"
               alt="<?php echo esc_attr($org['name']); ?>"
               style="width: 24px; height: 24px; object-fit: contain;">
        </div>
      <?php endif; ?>
      <div>
        <div style="color: #212529; font-size: 14px; font-weight: 600; margin-bottom: 2px;">
          <?php echo esc_html($org['name']); ?>
        </div>
        <?php if (!empty($org['industry'])): ?>
          <div style="color: #666; font-size: 12px;">
            <?php echo esc_html(ucwords($org['industry'])); ?>
          </div>
        <?php endif; ?>
      </div>
    </div>
    <?php
    return ob_get_clean();
  }
  
  /**
   * Render detailed style profile
   */
  private function render_detailed_profile($org, $atts) {
    // This would be an expanded version with more details
    // For now, return the card version with additional information
    return $this->render_card_profile($org, $atts);
  }
  
  /**
   * Company list shortcode
   * Usage: [baum_company_list limit="10" industry="technology"]
   */
  public function company_list_shortcode($atts) {
    $atts = shortcode_atts(array(
      'limit' => '10',
      'industry' => '',
      'country' => '',
      'size' => '',
      'style' => 'minimal',
      'columns' => '1'
    ), $atts, 'baum_company_list');
    
    $organizations = $this->get_organizations_list($atts);
    
    if (empty($organizations)) {
      return '<div class="baum-company-list-empty">No companies found.</div>';
    }
    
    ob_start();
    ?>
    <div class="baum-company-list" style="display: grid; grid-template-columns: repeat(<?php echo esc_attr($atts['columns']); ?>, 1fr); gap: 15px; margin: 20px 0;">
      <?php foreach ($organizations as $org): ?>
        <?php echo $this->render_minimal_profile($org, array('show_logo' => 'true')); ?>
      <?php endforeach; ?>
    </div>
    <?php
    return ob_get_clean();
  }
  
  /**
   * Get organizations list with filters
   */
  private function get_organizations_list($atts) {
    $db = BaumOrganizations::get_db_connection();
    if (!$db) {
      return array();
    }
    
    try {
      $sql = "SELECT * FROM organizations WHERE 1=1";
      $params = array();
      
      if (!empty($atts['industry'])) {
        $sql .= " AND industry LIKE :industry";
        $params[':industry'] = '%' . $atts['industry'] . '%';
      }
      
      if (!empty($atts['country'])) {
        $sql .= " AND country LIKE :country";
        $params[':country'] = '%' . $atts['country'] . '%';
      }
      
      if (!empty($atts['size'])) {
        $sql .= " AND size = :size";
        $params[':size'] = $atts['size'];
      }
      
      $sql .= " ORDER BY name ASC LIMIT :limit";
      $params[':limit'] = intval($atts['limit']);
      
      $stmt = $db->prepare($sql);
      $stmt->execute($params);
      
      return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
      error_log('Baum Organizations List Error: ' . $e->getMessage());
      return array();
    }
  }
  
  /**
   * Company search shortcode
   * Usage: [baum_company_search]
   */
  public function company_search_shortcode($atts) {
    $atts = shortcode_atts(array(
      'placeholder' => 'Search companies...',
      'button_text' => 'Search',
      'results_per_page' => '10'
    ), $atts, 'baum_company_search');
    
    ob_start();
    ?>
    <div class="baum-company-search" style="margin: 20px 0;">
      <form method="get" style="display: flex; gap: 10px; margin-bottom: 20px;">
        <input type="search" 
               name="baum_search" 
               placeholder="<?php echo esc_attr($atts['placeholder']); ?>"
               value="<?php echo esc_attr(isset($_GET['baum_search']) ? $_GET['baum_search'] : ''); ?>"
               style="flex: 1; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
        <button type="submit" 
                style="padding: 10px 20px; background: #007cba; color: white; border: none; border-radius: 4px; cursor: pointer;">
          <?php echo esc_html($atts['button_text']); ?>
        </button>
      </form>
      
      <?php if (isset($_GET['baum_search']) && !empty($_GET['baum_search'])): ?>
        <div class="baum-search-results">
          <?php echo $this->render_search_results($_GET['baum_search'], $atts['results_per_page']); ?>
        </div>
      <?php endif; ?>
    </div>
    <?php
    return ob_get_clean();
  }
  
  /**
   * Render search results
   */
  private function render_search_results($query, $limit) {
    $organizations = BaumOrganizations::search_organizations($query, $limit, 0);
    
    if (empty($organizations)) {
      return '<div class="baum-search-no-results">No companies found for "' . esc_html($query) . '".</div>';
    }
    
    ob_start();
    ?>
    <div class="baum-search-results-list">
      <h4>Search Results for "<?php echo esc_html($query); ?>" (<?php echo count($organizations); ?> found)</h4>
      <?php foreach ($organizations as $org): ?>
        <?php echo $this->render_minimal_profile($org, array('show_logo' => 'true')); ?>
      <?php endforeach; ?>
    </div>
    <?php
    return ob_get_clean();
  }
}

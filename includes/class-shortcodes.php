<?php
/**
 * Shortcodes for Baum Organizations plugin
 */

// Prevent direct access
if (!defined('ABSPATH')) {
  exit;
}

class BaumOrganizations_Shortcodes {
  
  /**
   * Constructor
   */
  public function __construct() {
    add_action('init', array($this, 'register_shortcodes'));
  }
  
  /**
   * Register all shortcodes
   */
  public function register_shortcodes() {
    add_shortcode('baum_company', array($this, 'company_profile_shortcode'));
    add_shortcode('baum_company_list', array($this, 'company_list_shortcode'));
    add_shortcode('baum_company_search', array($this, 'company_search_shortcode'));
  }
  
  /**
   * Company profile shortcode
   * Usage: [baum_company name="Apple Inc."] or [baum_company id="123"]
   */
  public function company_profile_shortcode($atts) {
    $atts = shortcode_atts(array(
      'name' => '',
      'id' => '',
      'style' => 'card', // card, minimal, detailed
      'show_logo' => 'true',
      'show_website' => 'true',
      'show_linkedin' => 'true',
      'show_location' => 'true',
      'show_industry' => 'true',
      'show_size' => 'true',
      'show_founded' => 'true',
      'width' => '400px'
    ), $atts, 'baum_company');
    
    // Get organization data
    $organization = $this->get_organization($atts['name'], $atts['id']);
    
    if (!$organization) {
      return '<div class="baum-company-error">Company not found.</div>';
    }
    
    // Generate output based on style
    switch ($atts['style']) {
      case 'minimal':
        return $this->render_minimal_profile($organization, $atts);
      case 'detailed':
        return $this->render_detailed_profile($organization, $atts);
      default:
        return $this->render_card_profile($organization, $atts);
    }
  }
  
  /**
   * Get organization from database
   */
  private function get_organization($name = '', $id = '') {
    $db = BaumOrganizations::get_db_connection();
    if (!$db) {
      return false;
    }
    
    try {
      if (!empty($id)) {
        $stmt = $db->prepare("SELECT * FROM organizations WHERE id = :id LIMIT 1");
        $stmt->execute(array(':id' => $id));
      } elseif (!empty($name)) {
        $stmt = $db->prepare("SELECT * FROM organizations WHERE name LIKE :name LIMIT 1");
        $stmt->execute(array(':name' => '%' . $name . '%'));
      } else {
        return false;
      }
      
      return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
      error_log('Baum Organizations Shortcode Error: ' . $e->getMessage());
      return false;
    }
  }
  
  /**
   * Render card style profile (WordPress-safe HTML)
   */
  private function render_card_profile($org, $atts) {
    $logo_url = '';
    if ($atts['show_logo'] === 'true' && !empty($org['logo_filename'])) {
      $logo_url = BAUM_ORGS_LOGOS_URL . $org['logo_filename'];
    }

    $country_flag = BaumOrganizations_Utils::get_country_flag($org['country']);
    $formatted_size = BaumOrganizations_Utils::format_company_size($org['size']);

    // Build HTML string safely
    $html = '<div class="baum-company-profile-wrapper" style="max-width: ' . esc_attr($atts['width']) . '; margin: 20px 0;">';
    $html .= '<div class="baum-company-card" style="background: white; border: 1px solid #e1e5e9; border-radius: 16px; padding: 20px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif;">';

    // Company Header
    $html .= '<div class="company-header" style="display: flex; align-items: center; gap: 15px; margin-bottom: 20px;">';

    if ($atts['show_logo'] === 'true') {
      $html .= '<div class="logo-container" style="width: 60px; height: 60px; background: #000; border-radius: 12px; display: flex; align-items: center; justify-content: center; flex-shrink: 0;">';
      if ($logo_url) {
        $html .= '<img src="' . esc_url($logo_url) . '" alt="' . esc_attr($org['name']) . '" style="width: 40px; height: 40px; object-fit: contain;">';
      } else {
        $html .= '<span style="color: white; font-size: 24px;">🏢</span>';
      }
      $html .= '</div>';
    }

    $html .= '<div class="company-info">';
    $html .= '<h3 style="margin: 0 0 5px 0; color: #212529; font-size: 18px; font-weight: 600;">' . esc_html($org['name']) . '</h3>';

    if ($atts['show_industry'] === 'true' && !empty($org['industry'])) {
      $html .= '<div style="display: flex; gap: 10px; align-items: center;">';
      $html .= '<span style="background: #f0f0f0; color: #666; padding: 2px 8px; border-radius: 4px; font-size: 12px; font-weight: 600;">';
      $html .= esc_html(strtoupper(substr($org['name'], 0, 4)));
      $html .= '</span>';
      $html .= '<span style="color: #666; font-size: 14px;">' . esc_html(ucwords($org['industry'])) . '</span>';
      $html .= '</div>';
    }

    $html .= '</div></div>';

    // Description
    $html .= '<div class="description-section" style="margin-bottom: 20px;">';
    $html .= '<h4 style="margin: 0 0 8px 0; color: #212529; font-size: 14px; font-weight: 600;">Description</h4>';
    $html .= '<p style="margin: 0; color: #666; font-size: 13px; line-height: 1.4;">';
    $html .= esc_html($org['name']);
    if (!empty($org['industry'])) {
      $html .= ' operates in the ' . esc_html(strtolower($org['industry'])) . ' industry';
    }
    if (!empty($org['locality']) && !empty($org['country'])) {
      $html .= ' and is based in ' . esc_html($org['locality']) . ', ' . esc_html($org['country']) . '.';
    }
    if ($atts['show_website'] === 'true' && !empty($org['website'])) {
      $html .= ' <a href="https://' . esc_attr($org['website']) . '" target="_blank" style="color: #007aff; text-decoration: none;">Read more</a>';
    }
    $html .= '</p></div>';

    // About Section
    $html .= '<div class="about-section" style="margin-bottom: 20px;">';
    $html .= '<h4 style="margin: 0 0 12px 0; color: #212529; font-size: 14px; font-weight: 600;">About</h4>';
    $html .= '<div class="about-grid" style="display: grid; gap: 8px;">';

    if ($atts['show_size'] === 'true' && !empty($org['size'])) {
      $html .= '<div style="display: flex; justify-content: space-between; align-items: center;">';
      $html .= '<span style="color: #666; font-size: 13px;">Employees</span>';
      $html .= '<span style="color: #212529; font-size: 13px; font-weight: 500;">' . esc_html($formatted_size) . '</span>';
      $html .= '</div>';
    }

    if ($atts['show_location'] === 'true' && (!empty($org['locality']) || !empty($org['region']) || !empty($org['country']))) {
      $html .= '<div style="display: flex; justify-content: space-between; align-items: flex-start;">';
      $html .= '<span style="color: #666; font-size: 13px;">Address</span>';
      $html .= '<div style="text-align: right;">';
      if (!empty($org['locality'])) {
        $html .= '<div style="color: #212529; font-size: 13px; font-weight: 500;">' . esc_html(ucwords($org['locality'])) . '</div>';
      }
      if (!empty($org['region'])) {
        $html .= '<div style="color: #212529; font-size: 13px; font-weight: 500;">' . esc_html(ucwords($org['region'])) . '</div>';
      }
      if (!empty($org['country'])) {
        $html .= '<div style="color: #212529; font-size: 13px; font-weight: 500;">' . esc_html(ucwords($org['country'])) . '</div>';
      }
      $html .= '</div></div>';
    }

    if ($atts['show_website'] === 'true' && !empty($org['website'])) {
      $html .= '<div style="display: flex; justify-content: space-between; align-items: center;">';
      $html .= '<span style="color: #666; font-size: 13px;">Website</span>';
      $html .= '<a href="https://' . esc_attr($org['website']) . '" target="_blank" rel="noopener" style="color: #007aff; font-size: 13px; text-decoration: none;">';
      $html .= esc_html($org['website']) . '</a></div>';
    }

    if ($atts['show_industry'] === 'true' && !empty($org['industry'])) {
      $html .= '<div style="display: flex; justify-content: space-between; align-items: center;">';
      $html .= '<span style="color: #666; font-size: 13px;">Industry</span>';
      $html .= '<span style="color: #212529; font-size: 13px; font-weight: 500;">' . esc_html(ucwords($org['industry'])) . '</span>';
      $html .= '</div>';
    }

    if ($atts['show_founded'] === 'true' && !empty($org['founded'])) {
      $html .= '<div style="display: flex; justify-content: space-between; align-items: center;">';
      $html .= '<span style="color: #666; font-size: 13px;">Founded</span>';
      $html .= '<span style="color: #212529; font-size: 13px; font-weight: 500;">' . esc_html($org['founded']) . '</span>';
      $html .= '</div>';
    }

    $html .= '</div></div>';

    $html .= '</div></div>';

    return $html;
  }
  
  /**
   * Render minimal style profile
   */
  private function render_minimal_profile($org, $atts) {
    $logo_url = '';
    if ($atts['show_logo'] === 'true' && !empty($org['logo_filename'])) {
      $logo_url = BAUM_ORGS_LOGOS_URL . $org['logo_filename'];
    }

    ob_start();
    ?>
    <div style="display: flex; align-items: center; gap: 12px; padding: 12px; background: white; border: 1px solid #e1e5e9; border-radius: 8px; margin: 8px 0; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
      <?php if ($logo_url): ?>
        <div style="width: 40px; height: 40px; background: #000; border-radius: 8px; display: flex; align-items: center; justify-content: center; flex-shrink: 0;">
          <img src="<?php echo esc_url($logo_url); ?>"
               alt="<?php echo esc_attr($org['name']); ?>"
               style="width: 24px; height: 24px; object-fit: contain;">
        </div>
      <?php endif; ?>
      <div>
        <div style="color: #212529; font-size: 14px; font-weight: 600; margin-bottom: 2px;">
          <?php echo esc_html($org['name']); ?>
        </div>
        <?php if (!empty($org['industry'])): ?>
          <div style="color: #666; font-size: 12px;">
            <?php echo esc_html(ucwords($org['industry'])); ?>
          </div>
        <?php endif; ?>
      </div>
    </div>
    <?php
    return ob_get_clean();
  }
  
  /**
   * Render detailed style profile
   */
  private function render_detailed_profile($org, $atts) {
    // This would be an expanded version with more details
    // For now, return the card version with additional information
    return $this->render_card_profile($org, $atts);
  }
  
  /**
   * Company list shortcode
   * Usage: [baum_company_list limit="10" industry="technology"]
   */
  public function company_list_shortcode($atts) {
    $atts = shortcode_atts(array(
      'limit' => '10',
      'industry' => '',
      'country' => '',
      'size' => '',
      'style' => 'minimal',
      'columns' => '1'
    ), $atts, 'baum_company_list');
    
    $organizations = $this->get_organizations_list($atts);
    
    if (empty($organizations)) {
      return '<div class="baum-company-list-empty">No companies found.</div>';
    }
    
    ob_start();
    ?>
    <div class="baum-company-list" style="display: grid; grid-template-columns: repeat(<?php echo esc_attr($atts['columns']); ?>, 1fr); gap: 15px; margin: 20px 0;">
      <?php foreach ($organizations as $org): ?>
        <?php echo $this->render_minimal_profile($org, array('show_logo' => 'true')); ?>
      <?php endforeach; ?>
    </div>
    <?php
    return ob_get_clean();
  }
  
  /**
   * Get organizations list with filters
   */
  private function get_organizations_list($atts) {
    $db = BaumOrganizations::get_db_connection();
    if (!$db) {
      return array();
    }
    
    try {
      $sql = "SELECT * FROM organizations WHERE 1=1";
      $params = array();
      
      if (!empty($atts['industry'])) {
        $sql .= " AND industry LIKE :industry";
        $params[':industry'] = '%' . $atts['industry'] . '%';
      }
      
      if (!empty($atts['country'])) {
        $sql .= " AND country LIKE :country";
        $params[':country'] = '%' . $atts['country'] . '%';
      }
      
      if (!empty($atts['size'])) {
        $sql .= " AND size = :size";
        $params[':size'] = $atts['size'];
      }
      
      $sql .= " ORDER BY name ASC LIMIT :limit";
      $params[':limit'] = intval($atts['limit']);
      
      $stmt = $db->prepare($sql);
      $stmt->execute($params);
      
      return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
      error_log('Baum Organizations List Error: ' . $e->getMessage());
      return array();
    }
  }
  
  /**
   * Company search shortcode
   * Usage: [baum_company_search]
   */
  public function company_search_shortcode($atts) {
    $atts = shortcode_atts(array(
      'placeholder' => 'Search companies...',
      'button_text' => 'Search',
      'results_per_page' => '10'
    ), $atts, 'baum_company_search');
    
    ob_start();
    ?>
    <div class="baum-company-search" style="margin: 20px 0;">
      <form method="get" style="display: flex; gap: 10px; margin-bottom: 20px;">
        <input type="search" 
               name="baum_search" 
               placeholder="<?php echo esc_attr($atts['placeholder']); ?>"
               value="<?php echo esc_attr(isset($_GET['baum_search']) ? $_GET['baum_search'] : ''); ?>"
               style="flex: 1; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
        <button type="submit" 
                style="padding: 10px 20px; background: #007cba; color: white; border: none; border-radius: 4px; cursor: pointer;">
          <?php echo esc_html($atts['button_text']); ?>
        </button>
      </form>
      
      <?php if (isset($_GET['baum_search']) && !empty($_GET['baum_search'])): ?>
        <div class="baum-search-results">
          <?php echo $this->render_search_results($_GET['baum_search'], $atts['results_per_page']); ?>
        </div>
      <?php endif; ?>
    </div>
    <?php
    return ob_get_clean();
  }
  
  /**
   * Render search results
   */
  private function render_search_results($query, $limit) {
    $organizations = BaumOrganizations::search_organizations($query, $limit, 0);
    
    if (empty($organizations)) {
      return '<div class="baum-search-no-results">No companies found for "' . esc_html($query) . '".</div>';
    }
    
    ob_start();
    ?>
    <div class="baum-search-results-list">
      <h4>Search Results for "<?php echo esc_html($query); ?>" (<?php echo count($organizations); ?> found)</h4>
      <?php foreach ($organizations as $org): ?>
        <?php echo $this->render_minimal_profile($org, array('show_logo' => 'true')); ?>
      <?php endforeach; ?>
    </div>
    <?php
    return ob_get_clean();
  }
}

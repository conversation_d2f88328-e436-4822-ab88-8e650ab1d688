<?php
/**
 * Utility class for Baum Organizations plugin
 */

// Prevent direct access
if (!defined('ABSPATH')) {
  exit;
}

class BaumOrganizations_Utils {
  
  /**
   * Cache for logo filenames to avoid repeated file system checks
   */
  private static $logo_cache = array();
  
  /**
   * Get logo filename for an organization
   * 
   * @param string $organization_name The organization name
   * @return string|false The logo filename or false if not found
   */
  public static function get_logo_filename($organization_name) {
    if (empty($organization_name)) {
      return false;
    }
    
    // Check cache first
    $cache_key = md5(strtolower($organization_name));
    if (isset(self::$logo_cache[$cache_key])) {
      return self::$logo_cache[$cache_key];
    }
    
    // Normalize the organization name for logo matching
    $normalized_name = self::normalize_name_for_logo($organization_name);
    
    // Try different variations of the name
    $variations = self::get_name_variations($normalized_name);
    
    foreach ($variations as $variation) {
      $logo_path = BAUM_ORGS_LOGOS_DIR . $variation . '.svg';
      if (file_exists($logo_path)) {
        self::$logo_cache[$cache_key] = $variation . '.svg';
        return $variation . '.svg';
      }
    }
    
    // Cache negative result to avoid repeated checks
    self::$logo_cache[$cache_key] = false;
    return false;
  }
  
  /**
   * Normalize organization name for logo filename matching
   * 
   * @param string $name The organization name
   * @return string The normalized name
   */
  private static function normalize_name_for_logo($name) {
    // Convert to lowercase
    $name = strtolower($name);
    
    // Remove common business suffixes
    $suffixes = array(
      ' inc', ' inc.', ' incorporated',
      ' ltd', ' ltd.', ' limited',
      ' llc', ' l.l.c.', ' corp', ' corp.',
      ' corporation', ' company', ' co',
      ' co.', ' group', ' holdings',
      ' international', ' intl', ' global',
      ' worldwide', ' enterprises', ' solutions',
      ' services', ' systems', ' technologies',
      ' tech', ' software', ' consulting'
    );
    
    foreach ($suffixes as $suffix) {
      if (substr($name, -strlen($suffix)) === $suffix) {
        $name = substr($name, 0, -strlen($suffix));
        break;
      }
    }
    
    // Remove special characters and replace with allowed characters
    $name = preg_replace('/[^a-z0-9\s\-_&]/', '', $name);
    
    // Replace spaces and multiple separators with single separator
    $name = preg_replace('/[\s\-_]+/', '', $name);
    
    // Handle special cases for common replacements
    $replacements = array(
      '&' => 'and',
      'at&t' => 'atandt',
      'h&m' => 'handm',
      'p&g' => 'pandg',
      'r&d' => 'randd'
    );
    
    foreach ($replacements as $search => $replace) {
      $name = str_replace($search, $replace, $name);
    }
    
    return trim($name);
  }
  
  /**
   * Get variations of a name to try for logo matching
   * 
   * @param string $name The normalized name
   * @return array Array of name variations to try
   */
  private static function get_name_variations($name) {
    $variations = array();
    
    // Original normalized name
    $variations[] = $name;
    
    // Try with dots for abbreviations
    if (strlen($name) <= 5 && ctype_alpha($name)) {
      $variations[] = implode('.', str_split($name));
      $variations[] = implode('dot', str_split($name));
    }
    
    // Try common abbreviations and expansions
    $abbreviations = array(
      'microsoft' => 'ms',
      'international' => 'intl',
      'corporation' => 'corp',
      'company' => 'co',
      'limited' => 'ltd',
      'incorporated' => 'inc',
      'technologies' => 'tech',
      'systems' => 'sys',
      'solutions' => 'sol'
    );
    
    foreach ($abbreviations as $full => $abbr) {
      if (strpos($name, $full) !== false) {
        $variations[] = str_replace($full, $abbr, $name);
      }
      if (strpos($name, $abbr) !== false) {
        $variations[] = str_replace($abbr, $full, $name);
      }
    }
    
    // Try without common words
    $common_words = array('the', 'a', 'an', 'of', 'for', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'by');
    $words = explode(' ', $name);
    $filtered_words = array_diff($words, $common_words);
    if (count($filtered_words) !== count($words)) {
      $variations[] = implode('', $filtered_words);
    }
    
    // Try first word only for multi-word names
    if (strpos($name, ' ') !== false) {
      $first_word = explode(' ', $name)[0];
      if (strlen($first_word) > 2) {
        $variations[] = $first_word;
      }
    }
    
    // Remove duplicates and empty values
    $variations = array_unique(array_filter($variations));
    
    return $variations;
  }
  
  /**
   * Sanitize input data
   * 
   * @param mixed $data The data to sanitize
   * @return mixed The sanitized data
   */
  public static function sanitize_input($data) {
    if (is_array($data)) {
      return array_map(array(self, 'sanitize_input'), $data);
    }
    
    if (is_string($data)) {
      return sanitize_text_field($data);
    }
    
    return $data;
  }
  
  /**
   * Format company size for display
   * 
   * @param string $size The company size string
   * @return string The formatted size
   */
  public static function format_company_size($size) {
    if (empty($size)) {
      return __('Unknown', 'baum-organizations');
    }
    
    $size_mappings = array(
      '1-10' => __('1-10 employees', 'baum-organizations'),
      '11-50' => __('11-50 employees', 'baum-organizations'),
      '51-200' => __('51-200 employees', 'baum-organizations'),
      '201-500' => __('201-500 employees', 'baum-organizations'),
      '501-1000' => __('501-1000 employees', 'baum-organizations'),
      '1001-5000' => __('1001-5000 employees', 'baum-organizations'),
      '5001-10000' => __('5001-10000 employees', 'baum-organizations'),
      '10000+' => __('10000+ employees', 'baum-organizations')
    );
    
    return isset($size_mappings[$size]) ? $size_mappings[$size] : $size;
  }
  
  /**
   * Get country flag emoji
   * 
   * @param string $country The country name
   * @return string The flag emoji or empty string
   */
  public static function get_country_flag($country) {
    $country_flags = array(
      'united states' => '🇺🇸',
      'united kingdom' => '🇬🇧',
      'canada' => '🇨🇦',
      'germany' => '🇩🇪',
      'france' => '🇫🇷',
      'italy' => '🇮🇹',
      'spain' => '🇪🇸',
      'netherlands' => '🇳🇱',
      'sweden' => '🇸🇪',
      'norway' => '🇳🇴',
      'denmark' => '🇩🇰',
      'finland' => '🇫🇮',
      'poland' => '🇵🇱',
      'russia' => '🇷🇺',
      'china' => '🇨🇳',
      'japan' => '🇯🇵',
      'south korea' => '🇰🇷',
      'india' => '🇮🇳',
      'australia' => '🇦🇺',
      'brazil' => '🇧🇷',
      'mexico' => '🇲🇽',
      'argentina' => '🇦🇷',
      'chile' => '🇨🇱',
      'south africa' => '🇿🇦',
      'israel' => '🇮🇱',
      'turkey' => '🇹🇷',
      'singapore' => '🇸🇬',
      'switzerland' => '🇨🇭',
      'austria' => '🇦🇹',
      'belgium' => '🇧🇪',
      'ireland' => '🇮🇪',
      'portugal' => '🇵🇹',
      'greece' => '🇬🇷',
      'czech republic' => '🇨🇿',
      'hungary' => '🇭🇺',
      'romania' => '🇷🇴',
      'bulgaria' => '🇧🇬',
      'croatia' => '🇭🇷',
      'slovenia' => '🇸🇮',
      'slovakia' => '🇸🇰',
      'estonia' => '🇪🇪',
      'latvia' => '🇱🇻',
      'lithuania' => '🇱🇹'
    );
    
    $country_lower = strtolower($country);
    return isset($country_flags[$country_lower]) ? $country_flags[$country_lower] : '';
  }
  
  /**
   * Build search index for organizations
   * 
   * @param array $organization The organization data
   * @return string The search index string
   */
  public static function build_search_index($organization) {
    $searchable_fields = array(
      'name', 'website', 'industry', 'locality', 
      'region', 'country', 'linkedin_url'
    );
    
    $search_terms = array();
    foreach ($searchable_fields as $field) {
      if (!empty($organization[$field])) {
        $search_terms[] = strtolower($organization[$field]);
      }
    }
    
    return implode(' ', $search_terms);
  }
}

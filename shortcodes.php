<div style="max-width: 400px; margin: 20px 0;">
  <h3 style="color: var(--color-body-text);">Company Profile Card</h3>
  <div class="company-profile-card" style="background: white; border: 1px solid #e0e0e0; border-radius: var(--border-radius); padding: 20px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
    <!-- Company Header -->
    <div style="display: flex; align-items: center; gap: 15px; margin-bottom: 20px;">
      <div style="width: 60px; height: 60px; background: #000; border-radius: var(--border-radius); display: flex; align-items: center; justify-content: center; color: white; font-size: 24px; font-weight: bold;">
        🍎
      </div>
      <div>
        <h3 style="margin: 0 0 5px 0; color: var(--color-body-text); font-size: 18px; font-weight: 600;">Apple Inc.</h3>
        <div style="display: flex; gap: 10px; align-items: center;">
          <span style="background: #f0f0f0; color: #666; padding: 2px 8px; border-radius: 4px; font-size: 12px; font-weight: 600;">AAPL</span>
          <span style="color: #666; font-size: 14px;">NASDAQ</span>
        </div>
      </div>
    </div>

    <!-- Description -->
    <div style="margin-bottom: 20px;">
      <h4 style="margin: 0 0 8px 0; color: var(--color-body-text); font-size: 14px; font-weight: 600;">Description</h4>
      <p style="margin: 0; color: #666; font-size: 13px; line-height: 1.4;">
        Apple Inc. designs, manufactures, and markets smartphones, personal computers, tablets, wearables, and accessories worldwide.
        <a href="#" style="color: var(--color-primary); text-decoration: none;">Read more</a>
      </p>
    </div>

    <!-- About Section -->
    <div style="margin-bottom: 20px;">
      <h4 style="margin: 0 0 12px 0; color: var(--color-body-text); font-size: 14px; font-weight: 600;">About</h4>
      <div style="display: grid; gap: 8px;">
        <div style="display: flex; justify-content: space-between; align-items: center;">
          <span style="color: #666; font-size: 13px;">CEO</span>
          <span style="color: var(--color-body-text); font-size: 13px; font-weight: 500;">Mr. Timothy D. Cook</span>
        </div>
        <div style="display: flex; justify-content: space-between; align-items: center;">
          <span style="color: #666; font-size: 13px;">Employees</span>
          <span style="color: var(--color-body-text); font-size: 13px; font-weight: 500;">161,000</span>
        </div>
        <div style="display: flex; justify-content: space-between; align-items: flex-start;">
          <span style="color: #666; font-size: 13px;">Address</span>
          <div style="text-align: right;">
            <div style="color: var(--color-body-text); font-size: 13px; font-weight: 500;">One Apple Park Way</div>
            <div style="color: var(--color-body-text); font-size: 13px; font-weight: 500;">Cupertino, 95014, CA</div>
            <div style="color: var(--color-body-text); font-size: 13px; font-weight: 500;">United States</div>
          </div>
        </div>
        <div style="display: flex; justify-content: space-between; align-items: center;">
          <span style="color: #666; font-size: 13px;">Website</span>
          <a href="#" style="color: var(--color-primary); font-size: 13px; text-decoration: none;">apple.com</a>
        </div>
        <div style="display: flex; justify-content: space-between; align-items: center;">
          <span style="color: #666; font-size: 13px;">Sector</span>
          <span style="color: var(--color-body-text); font-size: 13px; font-weight: 500;">Technology</span>
        </div>
        <div style="display: flex; justify-content: space-between; align-items: center;">
          <span style="color: #666; font-size: 13px;">Industry</span>
          <span style="color: var(--color-body-text); font-size: 13px; font-weight: 500;">Consumer electronics</span>
        </div>
        <div style="display: flex; justify-content: space-between; align-items: center;">
          <span style="color: #666; font-size: 13px;">MIC code</span>
          <span style="color: var(--color-body-text); font-size: 13px; font-weight: 500;">XNAS</span>
        </div>
      </div>
    </div>

    <!-- Earnings Chart -->
    <div>
      <h4 style="margin: 0 0 12px 0; color: var(--color-body-text); font-size: 14px; font-weight: 600;">Earnings</h4>
      <div style="display: flex; align-items: end; gap: 8px; height: 80px;">
        <div style="display: flex; flex-direction: column; align-items: center; flex: 1;">
          <div style="background: #007aff; height: 45px; width: 100%; border-radius: 4px 4px 0 0; display: flex; align-items: center; justify-content: center; color: white; font-size: 11px; font-weight: 600;">1.19</div>
          <div style="font-size: 10px; color: #666; margin-top: 4px; text-align: center;">2023-06-29</div>
        </div>
        <div style="display: flex; flex-direction: column; align-items: center; flex: 1;">
          <div style="background: #34c759; height: 50px; width: 100%; border-radius: 4px 4px 0 0; display: flex; align-items: center; justify-content: center; color: white; font-size: 11px; font-weight: 600;">1.26</div>
          <div style="font-size: 10px; color: #666; margin-top: 4px; text-align: center;">2023-09-29</div>
        </div>
        <div style="display: flex; flex-direction: column; align-items: center; flex: 1;">
          <div style="background: #007aff; height: 55px; width: 100%; border-radius: 4px 4px 0 0; display: flex; align-items: center; justify-content: center; color: white; font-size: 11px; font-weight: 600;">1.39</div>
          <div style="font-size: 10px; color: #666; margin-top: 4px; text-align: center;">2023-09-29</div>
        </div>
        <div style="display: flex; flex-direction: column; align-items: center; flex: 1;">
          <div style="background: #34c759; height: 58px; width: 100%; border-radius: 4px 4px 0 0; display: flex; align-items: center; justify-content: center; color: white; font-size: 11px; font-weight: 600;">1.46</div>
          <div style="font-size: 10px; color: #666; margin-top: 4px; text-align: center;">2023-12-30</div>
        </div>
        <div style="display: flex; flex-direction: column; align-items: center; flex: 1;">
          <div style="background: #007aff; height: 65px; width: 100%; border-radius: 4px 4px 0 0; display: flex; align-items: center; justify-content: center; color: white; font-size: 11px; font-weight: 600;">1.92</div>
          <div style="font-size: 10px; color: #666; margin-top: 4px; text-align: center;">2023-12-30</div>
        </div>
        <div style="display: flex; flex-direction: column; align-items: center; flex: 1;">
          <div style="background: #34c759; height: 70px; width: 100%; border-radius: 4px 4px 0 0; display: flex; align-items: center; justify-content: center; color: white; font-size: 11px; font-weight: 600;">2.00</div>
          <div style="font-size: 10px; color: #666; margin-top: 4px; text-align: center;">2023-12-30</div>
        </div>
      </div>
    </div>
  </div>
</div>

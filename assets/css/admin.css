/**
 * Admin styles for Baum Organizations plugin
 */

/* General admin styles */
.baum-orgs-admin {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Search form styling */
.baum-orgs-search-form {
  margin: 20px 0;
  background: #fff;
  padding: 15px;
  border: 1px solid #ccd0d4;
  border-radius: 4px;
  box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.baum-orgs-search-form .search-box {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 10px;
}

.baum-orgs-search-form input[type="search"] {
  flex: 1;
  max-width: 300px;
}

/* Results summary */
.baum-orgs-results-summary {
  margin: 10px 0;
  font-style: italic;
  color: #666;
  background: #f9f9f9;
  padding: 10px;
  border-left: 4px solid #0073aa;
}

/* Organization table styling */
.baum-orgs-table {
  background: #fff;
  border: 1px solid #ccd0d4;
  border-radius: 4px;
  overflow: hidden;
}

.baum-orgs-table .wp-list-table {
  border: none;
  box-shadow: none;
}

/* Logo column */
.column-logo {
  width: 60px;
  text-align: center;
}

.org-logo {
  border-radius: 4px;
  border: 1px solid #ddd;
  background: #fff;
  padding: 2px;
  transition: transform 0.2s ease;
}

.org-logo:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.org-logo-placeholder {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #f0f0f0, #e0e0e0);
  border: 1px solid #ddd;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  margin: 0 auto;
}

/* Name column */
.column-name {
  width: 200px;
}

.column-name strong {
  color: #0073aa;
  font-weight: 600;
}

.linkedin-link {
  font-size: 12px;
  color: #0077b5;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  margin-top: 4px;
}

.linkedin-link:hover {
  text-decoration: underline;
  color: #005885;
}

.linkedin-link .dashicons {
  font-size: 14px;
  width: 14px;
  height: 14px;
}

/* Website column */
.column-website {
  width: 180px;
}

.column-website a {
  color: #0073aa;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.column-website a:hover {
  text-decoration: underline;
}

.column-website .dashicons {
  font-size: 12px;
  width: 12px;
  height: 12px;
}

/* Industry column */
.column-industry {
  width: 150px;
}

/* Size column */
.column-size {
  width: 120px;
  font-weight: 500;
}

/* Location column */
.column-location {
  width: 200px;
}

/* Founded column */
.column-founded {
  width: 80px;
  text-align: center;
  font-weight: 500;
}

/* Text utilities */
.text-muted {
  color: #999;
  font-style: italic;
}

/* Card styling */
.card {
  background: #fff;
  border: 1px solid #ccd0d4;
  border-radius: 4px;
  padding: 20px;
  margin: 20px 0;
  box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.card h2 {
  margin-top: 0;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
  color: #0073aa;
}

.card h3 {
  color: #0073aa;
  margin-top: 20px;
}

/* Notice styling */
.notice.inline {
  margin: 15px 0;
  padding: 12px;
}

/* Form styling */
.baum-orgs-form .form-table th {
  width: 200px;
  padding-left: 0;
}

/* Status indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  font-weight: 500;
}

.status-indicator.success {
  color: #00a32a;
}

.status-indicator.warning {
  color: #dba617;
}

.status-indicator.error {
  color: #d63638;
}

.status-indicator .dashicons {
  font-size: 16px;
  width: 16px;
  height: 16px;
}

/* Progress bar */
.baum-orgs-progress {
  background: #f0f0f0;
  border-radius: 10px;
  height: 20px;
  overflow: hidden;
  margin: 10px 0;
}

.baum-orgs-progress-bar {
  background: linear-gradient(90deg, #0073aa, #005a87);
  height: 100%;
  transition: width 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  font-weight: 500;
}

/* Statistics */
.baum-orgs-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin: 20px 0;
}

.baum-orgs-stat {
  background: #fff;
  border: 1px solid #ccd0d4;
  border-radius: 4px;
  padding: 20px;
  text-align: center;
  box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.baum-orgs-stat-number {
  font-size: 2em;
  font-weight: bold;
  color: #0073aa;
  display: block;
}

.baum-orgs-stat-label {
  color: #666;
  font-size: 0.9em;
  margin-top: 5px;
}

/* Responsive design */
@media (max-width: 768px) {
  .baum-orgs-search-form .search-box {
    flex-direction: column;
    align-items: stretch;
  }
  
  .baum-orgs-search-form input[type="search"] {
    max-width: none;
    margin-bottom: 10px;
  }
  
  .baum-orgs-stats {
    grid-template-columns: 1fr;
  }
  
  /* Hide less important columns on mobile */
  .column-founded,
  .column-size {
    display: none;
  }
}

/* Loading states */
.baum-orgs-loading {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  color: #666;
}

.baum-orgs-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #0073aa;
  border-radius: 50%;
  animation: baum-orgs-spin 1s linear infinite;
}

@keyframes baum-orgs-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Tooltips */
.baum-orgs-tooltip {
  position: relative;
  cursor: help;
}

.baum-orgs-tooltip:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: #333;
  color: white;
  padding: 6px 10px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 1000;
  margin-bottom: 5px;
}

.baum-orgs-tooltip:hover::before {
  content: '';
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 5px solid transparent;
  border-top-color: #333;
  z-index: 1000;
}

/* Code blocks */
pre code {
  background: #f1f1f1;
  padding: 15px;
  display: block;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.4;
  overflow-x: auto;
}

/* Button enhancements */
.button.button-large {
  padding: 8px 16px;
  font-size: 14px;
}

.button-group {
  display: inline-flex;
  gap: 10px;
  margin: 10px 0;
}

/* Accessibility improvements */
.screen-reader-text {
  clip: rect(1px, 1px, 1px, 1px);
  position: absolute !important;
  height: 1px;
  width: 1px;
  overflow: hidden;
}

/* Focus styles */
.baum-orgs-admin a:focus,
.baum-orgs-admin button:focus,
.baum-orgs-admin input:focus,
.baum-orgs-admin select:focus,
.baum-orgs-admin textarea:focus {
  outline: 2px solid #0073aa;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .baum-orgs-search-form,
  .button,
  .notice {
    display: none;
  }
  
  .baum-orgs-table {
    border: 1px solid #000;
  }
  
  .org-logo {
    max-width: 20px;
    max-height: 20px;
  }
}

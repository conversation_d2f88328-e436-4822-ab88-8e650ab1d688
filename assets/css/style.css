/**
 * Frontend styles for Baum Organizations plugin
 */

/* Organization display components */
.baum-orgs-container {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.baum-orgs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin: 20px 0;
}

.baum-orgs-card {
  background: #fff;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.baum-orgs-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.baum-orgs-card-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
}

.baum-orgs-logo {
  width: 48px;
  height: 48px;
  border-radius: 6px;
  border: 1px solid #e1e5e9;
  background: #f8f9fa;
  padding: 4px;
  flex-shrink: 0;
}

.baum-orgs-logo-placeholder {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border: 1px solid #e1e5e9;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6c757d;
  font-size: 20px;
  flex-shrink: 0;
}

.baum-orgs-card-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #212529;
  line-height: 1.3;
}

.baum-orgs-card-meta {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 15px;
}

.baum-orgs-meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #6c757d;
}

.baum-orgs-meta-icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.baum-orgs-card-links {
  display: flex;
  gap: 10px;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #e9ecef;
}

.baum-orgs-link {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: #f8f9fa;
  border: 1px solid #e1e5e9;
  border-radius: 4px;
  text-decoration: none;
  font-size: 13px;
  color: #495057;
  transition: all 0.2s ease;
}

.baum-orgs-link:hover {
  background: #e9ecef;
  border-color: #adb5bd;
  text-decoration: none;
  color: #212529;
}

.baum-orgs-link-website {
  background: #0d6efd;
  border-color: #0d6efd;
  color: white;
}

.baum-orgs-link-website:hover {
  background: #0b5ed7;
  border-color: #0a58ca;
  color: white;
}

.baum-orgs-link-linkedin {
  background: #0077b5;
  border-color: #0077b5;
  color: white;
}

.baum-orgs-link-linkedin:hover {
  background: #005885;
  border-color: #004d73;
  color: white;
}

/* Search form */
.baum-orgs-search {
  margin: 20px 0;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.baum-orgs-search-form {
  display: flex;
  gap: 10px;
  align-items: center;
}

.baum-orgs-search-input {
  flex: 1;
  padding: 10px 15px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  font-size: 16px;
  background: white;
}

.baum-orgs-search-input:focus {
  outline: none;
  border-color: #0d6efd;
  box-shadow: 0 0 0 3px rgba(13, 110, 253, 0.1);
}

.baum-orgs-search-button {
  padding: 10px 20px;
  background: #0d6efd;
  border: 1px solid #0d6efd;
  border-radius: 6px;
  color: white;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.baum-orgs-search-button:hover {
  background: #0b5ed7;
  border-color: #0a58ca;
}

/* Pagination */
.baum-orgs-pagination {
  display: flex;
  justify-content: center;
  gap: 5px;
  margin: 30px 0;
}

.baum-orgs-pagination a,
.baum-orgs-pagination span {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  height: 40px;
  padding: 0 10px;
  border: 1px solid #e1e5e9;
  border-radius: 6px;
  text-decoration: none;
  color: #495057;
  font-size: 14px;
  transition: all 0.2s ease;
}

.baum-orgs-pagination a:hover {
  background: #f8f9fa;
  border-color: #adb5bd;
  text-decoration: none;
}

.baum-orgs-pagination .current {
  background: #0d6efd;
  border-color: #0d6efd;
  color: white;
}

/* List view */
.baum-orgs-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.baum-orgs-list-item {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.baum-orgs-list-item:hover {
  border-color: #adb5bd;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.baum-orgs-list-logo {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  border: 1px solid #e1e5e9;
  background: #f8f9fa;
  padding: 2px;
  flex-shrink: 0;
}

.baum-orgs-list-content {
  flex: 1;
}

.baum-orgs-list-title {
  margin: 0 0 5px 0;
  font-size: 16px;
  font-weight: 600;
  color: #212529;
}

.baum-orgs-list-meta {
  font-size: 14px;
  color: #6c757d;
}

/* Filters */
.baum-orgs-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin: 20px 0;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.baum-orgs-filter {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.baum-orgs-filter label {
  font-size: 14px;
  font-weight: 500;
  color: #495057;
}

.baum-orgs-filter select,
.baum-orgs-filter input {
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  background: white;
}

/* Responsive design */
@media (max-width: 768px) {
  .baum-orgs-grid {
    grid-template-columns: 1fr;
  }
  
  .baum-orgs-search-form {
    flex-direction: column;
    align-items: stretch;
  }
  
  .baum-orgs-list-item {
    flex-direction: column;
    align-items: flex-start;
    text-align: left;
  }
  
  .baum-orgs-filters {
    flex-direction: column;
  }
  
  .baum-orgs-card-links {
    flex-direction: column;
  }
}

/* Loading states */
.baum-orgs-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #6c757d;
}

.baum-orgs-spinner {
  width: 24px;
  height: 24px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #0d6efd;
  border-radius: 50%;
  animation: baum-orgs-spin 1s linear infinite;
  margin-right: 10px;
}

@keyframes baum-orgs-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Empty states */
.baum-orgs-empty {
  text-align: center;
  padding: 60px 20px;
  color: #6c757d;
}

.baum-orgs-empty-icon {
  font-size: 48px;
  margin-bottom: 20px;
  opacity: 0.5;
}

.baum-orgs-empty-title {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 10px;
  color: #495057;
}

.baum-orgs-empty-text {
  font-size: 16px;
  line-height: 1.5;
}

/* Accessibility */
.baum-orgs-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus styles */
.baum-orgs-container a:focus,
.baum-orgs-container button:focus,
.baum-orgs-container input:focus,
.baum-orgs-container select:focus {
  outline: 2px solid #0d6efd;
  outline-offset: 2px;
}

/* Shortcode styles - Clean modern design */
.baum-company-profile {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Company card hover effects */
.baum-company-profile > div > div:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0,0,0,0.12) !important;
  transition: all 0.2s ease;
}

/* Minimal profile hover effects */
.baum-company-list > div:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 12px rgba(0,0,0,0.1) !important;
  transition: all 0.2s ease;
}

/* Error states */
.baum-company-error {
  padding: 16px;
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
  border-radius: 8px;
  margin: 12px 0;
  font-size: 14px;
}

.baum-company-list-empty {
  padding: 24px;
  text-align: center;
  color: #6b7280;
  font-style: italic;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin: 12px 0;
}

/* Search form styling */
.baum-company-search input[type="search"] {
  font-size: 16px;
  line-height: 1.5;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  background: white;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.baum-company-search input[type="search"]:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.baum-company-search button {
  font-weight: 500;
  padding: 12px 20px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.baum-company-search button:hover {
  background: #2563eb !important;
}

.baum-search-no-results {
  padding: 16px;
  background: #fefce8;
  color: #a16207;
  border: 1px solid #fde047;
  border-radius: 8px;
  margin: 12px 0;
}

.baum-search-results-list h4 {
  margin: 0 0 16px 0;
  color: #111827;
  font-size: 16px;
  font-weight: 600;
}

/* Responsive shortcode styles */
@media (max-width: 768px) {
  .baum-company-profile > div {
    max-width: 100% !important;
    margin: 16px 0 !important;
  }

  .baum-company-profile > div > div {
    padding: 16px !important;
  }

  .baum-company-search form {
    flex-direction: column !important;
    gap: 12px !important;
  }

  .baum-company-search input[type="search"] {
    margin-bottom: 0;
  }

  .baum-company-list {
    grid-template-columns: 1fr !important;
  }

  /* Stack company header on mobile */
  .baum-company-profile div[style*="display: flex"] {
    flex-direction: column !important;
    align-items: flex-start !important;
    gap: 12px !important;
  }
}

/* Print styles */
@media print {
  .baum-orgs-search,
  .baum-orgs-filters,
  .baum-orgs-pagination,
  .baum-company-search {
    display: none;
  }

  .baum-orgs-card,
  .baum-company-card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #000;
  }

  .baum-orgs-card:hover,
  .baum-company-card:hover {
    transform: none;
  }
}

/**
 * Admin JavaScript for Baum Organizations plugin
 */

(function($) {
  'use strict';
  
  // Initialize when document is ready
  $(document).ready(function() {
    initializeSearch();
    initializeTooltips();
    initializeImageLoading();
    initializeImportProgress();
  });
  
  /**
   * Initialize search functionality
   */
  function initializeSearch() {
    var $searchForm = $('.baum-orgs-search-form form');
    var $searchInput = $('#organization-search-input');
    
    if ($searchForm.length && $searchInput.length) {
      // Add search suggestions/autocomplete if needed
      $searchInput.on('input', debounce(function() {
        var query = $(this).val();
        if (query.length >= 2) {
          // Could implement live search suggestions here
          console.log('Search query:', query);
        }
      }, 300));
      
      // Handle form submission
      $searchForm.on('submit', function(e) {
        var query = $searchInput.val().trim();
        if (query === '') {
          // Remove search parameter if empty
          var url = new URL(window.location);
          url.searchParams.delete('search');
          window.location.href = url.toString();
          e.preventDefault();
        }
      });
    }
  }
  
  /**
   * Initialize tooltips
   */
  function initializeTooltips() {
    // Add tooltips to elements with data-tooltip attribute
    $('[data-tooltip]').each(function() {
      $(this).addClass('baum-orgs-tooltip');
    });
    
    // Add tooltips to logo placeholders
    $('.org-logo-placeholder').attr('data-tooltip', 'No logo available');
    
    // Add tooltips to external links
    $('a[target="_blank"]').each(function() {
      var $this = $(this);
      if (!$this.attr('data-tooltip')) {
        $this.attr('data-tooltip', 'Opens in new tab');
      }
    });
  }
  
  /**
   * Initialize image loading with fallbacks
   */
  function initializeImageLoading() {
    $('.org-logo').each(function() {
      var $img = $(this);
      var $placeholder = $('<div class="org-logo-placeholder"><span class="dashicons dashicons-building"></span></div>');
      
      $img.on('error', function() {
        // Replace broken image with placeholder
        $img.replaceWith($placeholder);
      });
      
      $img.on('load', function() {
        // Add loaded class for animations
        $img.addClass('loaded');
      });
    });
  }
  
  /**
   * Initialize import progress tracking
   */
  function initializeImportProgress() {
    var $importForm = $('form[action*="import"]');
    var $importButton = $('input[name="start_import"]');
    
    if ($importForm.length && $importButton.length) {
      $importForm.on('submit', function() {
        // Show loading state
        $importButton.prop('disabled', true);
        $importButton.val('Importing...');
        
        // Add progress indicator
        var $progress = $('<div class="baum-orgs-progress"><div class="baum-orgs-progress-bar" style="width: 0%">Starting...</div></div>');
        $importForm.after($progress);
        
        // Simulate progress (since we can't track real progress easily)
        simulateProgress($progress.find('.baum-orgs-progress-bar'));
      });
    }
  }
  
  /**
   * Simulate import progress
   */
  function simulateProgress($progressBar) {
    var progress = 0;
    var messages = [
      'Starting import...',
      'Reading dataset...',
      'Processing organizations...',
      'Matching logos...',
      'Building search index...',
      'Finalizing database...'
    ];
    
    var interval = setInterval(function() {
      progress += Math.random() * 15;
      if (progress > 95) {
        progress = 95;
      }
      
      var messageIndex = Math.floor((progress / 100) * messages.length);
      var message = messages[Math.min(messageIndex, messages.length - 1)];
      
      $progressBar.css('width', progress + '%').text(message);
      
      if (progress >= 95) {
        clearInterval(interval);
        $progressBar.text('Almost done...');
      }
    }, 1000);
  }
  
  /**
   * Debounce function to limit function calls
   */
  function debounce(func, wait, immediate) {
    var timeout;
    return function() {
      var context = this, args = arguments;
      var later = function() {
        timeout = null;
        if (!immediate) func.apply(context, args);
      };
      var callNow = immediate && !timeout;
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
      if (callNow) func.apply(context, args);
    };
  }
  
  /**
   * Show notification
   */
  function showNotification(message, type) {
    type = type || 'info';
    var $notice = $('<div class="notice notice-' + type + ' is-dismissible"><p>' + message + '</p></div>');
    
    $('.wrap h1').after($notice);
    
    // Auto-dismiss after 5 seconds
    setTimeout(function() {
      $notice.fadeOut(function() {
        $(this).remove();
      });
    }, 5000);
  }
  
  /**
   * Copy text to clipboard
   */
  function copyToClipboard(text) {
    if (navigator.clipboard && window.isSecureContext) {
      navigator.clipboard.writeText(text).then(function() {
        showNotification('Copied to clipboard!', 'success');
      });
    } else {
      // Fallback for older browsers
      var textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      
      try {
        document.execCommand('copy');
        showNotification('Copied to clipboard!', 'success');
      } catch (err) {
        showNotification('Failed to copy to clipboard', 'error');
      }
      
      textArea.remove();
    }
  }
  
  /**
   * Format numbers with commas
   */
  function formatNumber(num) {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }
  
  /**
   * Validate URL
   */
  function isValidUrl(string) {
    try {
      new URL(string);
      return true;
    } catch (_) {
      return false;
    }
  }
  
  /**
   * Get URL parameter
   */
  function getUrlParameter(name) {
    name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
    var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
    var results = regex.exec(location.search);
    return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
  }
  
  /**
   * Highlight search terms in text
   */
  function highlightSearchTerms(text, terms) {
    if (!terms || terms.length === 0) {
      return text;
    }
    
    var regex = new RegExp('(' + terms.join('|') + ')', 'gi');
    return text.replace(regex, '<mark>$1</mark>');
  }
  
  /**
   * Initialize keyboard shortcuts
   */
  function initializeKeyboardShortcuts() {
    $(document).on('keydown', function(e) {
      // Ctrl/Cmd + K to focus search
      if ((e.ctrlKey || e.metaKey) && e.keyCode === 75) {
        e.preventDefault();
        $('#organization-search-input').focus();
      }
      
      // Escape to clear search
      if (e.keyCode === 27) {
        var $searchInput = $('#organization-search-input');
        if ($searchInput.is(':focus') && $searchInput.val()) {
          $searchInput.val('').trigger('input');
        }
      }
    });
  }
  
  /**
   * Initialize data export functionality
   */
  function initializeDataExport() {
    $('.export-data').on('click', function(e) {
      e.preventDefault();
      
      var format = $(this).data('format') || 'csv';
      var $button = $(this);
      var originalText = $button.text();
      
      $button.text('Exporting...').prop('disabled', true);
      
      // Simulate export process
      setTimeout(function() {
        $button.text(originalText).prop('disabled', false);
        showNotification('Export completed!', 'success');
      }, 2000);
    });
  }
  
  /**
   * Initialize advanced search filters
   */
  function initializeAdvancedFilters() {
    var $advancedToggle = $('.advanced-search-toggle');
    var $advancedFilters = $('.advanced-search-filters');
    
    if ($advancedToggle.length && $advancedFilters.length) {
      $advancedToggle.on('click', function(e) {
        e.preventDefault();
        $advancedFilters.slideToggle();
        $(this).text(function(i, text) {
          return text === 'Show Advanced Filters' ? 'Hide Advanced Filters' : 'Show Advanced Filters';
        });
      });
    }
  }
  
  // Initialize additional features
  $(document).ready(function() {
    initializeKeyboardShortcuts();
    initializeDataExport();
    initializeAdvancedFilters();
  });
  
  // Expose utility functions globally
  window.BaumOrgsAdmin = {
    showNotification: showNotification,
    copyToClipboard: copyToClipboard,
    formatNumber: formatNumber,
    isValidUrl: isValidUrl,
    getUrlParameter: getUrlParameter,
    highlightSearchTerms: highlightSearchTerms
  };
  
})(jQuery);

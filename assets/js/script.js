/**
 * Frontend JavaScript for Baum Organizations plugin
 */

(function($) {
  'use strict';
  
  // Initialize when document is ready
  $(document).ready(function() {
    initializeOrganizationCards();
    initializeSearch();
    initializeLazyLoading();
    initializeFilters();
  });
  
  /**
   * Initialize organization cards
   */
  function initializeOrganizationCards() {
    $('.baum-orgs-card').each(function() {
      var $card = $(this);
      
      // Add hover effects
      $card.on('mouseenter', function() {
        $(this).addClass('hovered');
      }).on('mouseleave', function() {
        $(this).removeClass('hovered');
      });
      
      // Handle logo loading errors
      $card.find('.baum-orgs-logo').on('error', function() {
        var $img = $(this);
        var $placeholder = $('<div class="baum-orgs-logo-placeholder">🏢</div>');
        $img.replaceWith($placeholder);
      });
    });
  }
  
  /**
   * Initialize search functionality
   */
  function initializeSearch() {
    var $searchForm = $('.baum-orgs-search-form');
    var $searchInput = $('.baum-orgs-search-input');
    var $searchButton = $('.baum-orgs-search-button');
    
    if ($searchForm.length) {
      // Add live search with debouncing
      var searchTimeout;
      $searchInput.on('input', function() {
        clearTimeout(searchTimeout);
        var query = $(this).val().trim();
        
        if (query.length >= 2) {
          searchTimeout = setTimeout(function() {
            performLiveSearch(query);
          }, 500);
        } else if (query.length === 0) {
          clearSearchResults();
        }
      });
      
      // Handle form submission
      $searchForm.on('submit', function(e) {
        e.preventDefault();
        var query = $searchInput.val().trim();
        if (query) {
          performSearch(query);
        }
      });
      
      // Search button click
      $searchButton.on('click', function(e) {
        e.preventDefault();
        $searchForm.trigger('submit');
      });
    }
  }
  
  /**
   * Perform live search
   */
  function performLiveSearch(query) {
    // This would typically make an AJAX request
    // For now, we'll just highlight matching text
    highlightSearchResults(query);
  }
  
  /**
   * Perform full search
   */
  function performSearch(query) {
    showLoadingState();
    
    // Add query to URL and reload
    var url = new URL(window.location);
    url.searchParams.set('search', query);
    window.location.href = url.toString();
  }
  
  /**
   * Highlight search results
   */
  function highlightSearchResults(query) {
    var terms = query.toLowerCase().split(' ');
    
    $('.baum-orgs-card, .baum-orgs-list-item').each(function() {
      var $item = $(this);
      var text = $item.text().toLowerCase();
      var matches = terms.some(function(term) {
        return text.includes(term);
      });
      
      $item.toggleClass('search-match', matches);
    });
  }
  
  /**
   * Clear search results
   */
  function clearSearchResults() {
    $('.baum-orgs-card, .baum-orgs-list-item').removeClass('search-match');
  }
  
  /**
   * Initialize lazy loading for images
   */
  function initializeLazyLoading() {
    if ('IntersectionObserver' in window) {
      var imageObserver = new IntersectionObserver(function(entries, observer) {
        entries.forEach(function(entry) {
          if (entry.isIntersecting) {
            var img = entry.target;
            img.src = img.dataset.src;
            img.classList.remove('lazy');
            imageObserver.unobserve(img);
          }
        });
      });
      
      $('.baum-orgs-logo.lazy').each(function() {
        imageObserver.observe(this);
      });
    } else {
      // Fallback for older browsers
      $('.baum-orgs-logo.lazy').each(function() {
        var $img = $(this);
        $img.attr('src', $img.data('src')).removeClass('lazy');
      });
    }
  }
  
  /**
   * Initialize filters
   */
  function initializeFilters() {
    $('.baum-orgs-filters select, .baum-orgs-filters input').on('change', function() {
      applyFilters();
    });
  }
  
  /**
   * Apply filters
   */
  function applyFilters() {
    var filters = {};
    
    $('.baum-orgs-filters select, .baum-orgs-filters input').each(function() {
      var $input = $(this);
      var name = $input.attr('name');
      var value = $input.val();
      
      if (value && value !== '') {
        filters[name] = value;
      }
    });
    
    // Update URL with filters
    var url = new URL(window.location);
    Object.keys(filters).forEach(function(key) {
      url.searchParams.set(key, filters[key]);
    });
    
    // Remove empty filters
    Array.from(url.searchParams.keys()).forEach(function(key) {
      if (!filters[key]) {
        url.searchParams.delete(key);
      }
    });
    
    window.location.href = url.toString();
  }
  
  /**
   * Show loading state
   */
  function showLoadingState() {
    var $container = $('.baum-orgs-container');
    var $loading = $('<div class="baum-orgs-loading"><div class="baum-orgs-spinner"></div>Loading...</div>');
    
    $container.append($loading);
  }
  
  /**
   * Initialize keyboard shortcuts
   */
  function initializeKeyboardShortcuts() {
    $(document).on('keydown', function(e) {
      // Ctrl/Cmd + K to focus search
      if ((e.ctrlKey || e.metaKey) && e.keyCode === 75) {
        e.preventDefault();
        $('.baum-orgs-search-input').focus();
      }
      
      // Escape to clear search
      if (e.keyCode === 27) {
        var $searchInput = $('.baum-orgs-search-input');
        if ($searchInput.is(':focus')) {
          $searchInput.val('').trigger('input');
        }
      }
    });
  }
  
  /**
   * Initialize infinite scroll
   */
  function initializeInfiniteScroll() {
    var $window = $(window);
    var $document = $(document);
    var loading = false;
    var page = 2; // Start from page 2 since page 1 is already loaded
    
    $window.on('scroll', function() {
      if (loading) return;
      
      var scrollTop = $window.scrollTop();
      var windowHeight = $window.height();
      var documentHeight = $document.height();
      
      // Load more when user is 200px from bottom
      if (scrollTop + windowHeight >= documentHeight - 200) {
        loadMoreOrganizations(page);
        page++;
      }
    });
  }
  
  /**
   * Load more organizations via AJAX
   */
  function loadMoreOrganizations(page) {
    // This would make an AJAX request to load more organizations
    // Implementation depends on your backend setup
    console.log('Loading page:', page);
  }
  
  /**
   * Initialize view toggle
   */
  function initializeViewToggle() {
    $('.baum-orgs-view-toggle').on('click', 'button', function() {
      var $button = $(this);
      var view = $button.data('view');
      
      $button.addClass('active').siblings().removeClass('active');
      
      var $container = $('.baum-orgs-results');
      $container.removeClass('grid-view list-view').addClass(view + '-view');
      
      // Save preference
      localStorage.setItem('baum-orgs-view', view);
    });
    
    // Load saved preference
    var savedView = localStorage.getItem('baum-orgs-view');
    if (savedView) {
      $('.baum-orgs-view-toggle button[data-view="' + savedView + '"]').click();
    }
  }
  
  /**
   * Initialize tooltips
   */
  function initializeTooltips() {
    $('[data-tooltip]').each(function() {
      var $element = $(this);
      var tooltip = $element.data('tooltip');
      
      $element.on('mouseenter', function() {
        var $tooltip = $('<div class="baum-orgs-tooltip">' + tooltip + '</div>');
        $('body').append($tooltip);
        
        var offset = $element.offset();
        $tooltip.css({
          top: offset.top - $tooltip.outerHeight() - 10,
          left: offset.left + ($element.outerWidth() / 2) - ($tooltip.outerWidth() / 2)
        });
      }).on('mouseleave', function() {
        $('.baum-orgs-tooltip').remove();
      });
    });
  }
  
  /**
   * Initialize analytics tracking
   */
  function initializeAnalytics() {
    // Track organization card clicks
    $('.baum-orgs-card').on('click', 'a', function() {
      var orgName = $(this).closest('.baum-orgs-card').find('.baum-orgs-card-title').text();
      var linkType = $(this).hasClass('baum-orgs-link-website') ? 'website' : 'linkedin';
      
      // Send analytics event (if analytics is set up)
      if (typeof gtag !== 'undefined') {
        gtag('event', 'organization_link_click', {
          'organization_name': orgName,
          'link_type': linkType
        });
      }
    });
    
    // Track search queries
    $('.baum-orgs-search-form').on('submit', function() {
      var query = $('.baum-orgs-search-input').val();
      
      if (typeof gtag !== 'undefined') {
        gtag('event', 'search', {
          'search_term': query
        });
      }
    });
  }
  
  // Initialize additional features
  $(document).ready(function() {
    initializeKeyboardShortcuts();
    initializeViewToggle();
    initializeTooltips();
    initializeAnalytics();
    
    // Uncomment if you want infinite scroll
    // initializeInfiniteScroll();
  });
  
  // Expose utility functions globally
  window.BaumOrgs = {
    performSearch: performSearch,
    highlightSearchResults: highlightSearchResults,
    clearSearchResults: clearSearchResults,
    showLoadingState: showLoadingState
  };
  
})(jQuery);

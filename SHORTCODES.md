# Baum Organizations Shortcodes

This document explains how to use the shortcodes provided by the Baum Organizations plugin to display company information on your WordPress site.

## Available Shortcodes

### 1. `[baum_company]` - Display Single Company Profile

Display a detailed profile card for a specific company.

#### Basic Usage
```
[baum_company name="Apple Inc."]
[baum_company name="Microsoft"]
[baum_company id="123"]
```

#### Parameters
- **name** - Company name (partial matches work)
- **id** - Company ID from database
- **style** - Display style: `card` (default), `minimal`, `detailed`
- **show_logo** - Show company logo: `true` (default), `false`
- **show_website** - Show website link: `true` (default), `false`
- **show_linkedin** - Show LinkedIn link: `true` (default), `false`
- **show_location** - Show location info: `true` (default), `false`
- **show_industry** - Show industry: `true` (default), `false`
- **show_size** - Show company size: `true` (default), `false`
- **show_founded** - Show founded year: `true` (default), `false`
- **width** - Card width: `400px` (default)

#### Examples
```
<!-- Full company card -->
[baum_company name="Apple Inc."]

<!-- Minimal display without logo -->
[baum_company name="Google" style="minimal" show_logo="false"]

<!-- Custom width card -->
[baum_company name="Microsoft" width="500px"]

<!-- Hide specific information -->
[baum_company name="Amazon" show_linkedin="false" show_founded="false"]
```

### 2. `[baum_company_list]` - Display Multiple Companies

Display a list of companies with optional filtering.

#### Basic Usage
```
[baum_company_list]
[baum_company_list limit="5"]
[baum_company_list industry="technology"]
```

#### Parameters
- **limit** - Number of companies to show: `10` (default)
- **industry** - Filter by industry (partial matches)
- **country** - Filter by country (partial matches)
- **size** - Filter by company size (exact match)
- **style** - Display style: `minimal` (default), `card`
- **columns** - Number of columns: `1` (default)

#### Examples
```
<!-- Show 5 technology companies -->
[baum_company_list limit="5" industry="technology"]

<!-- Show companies from United States -->
[baum_company_list country="United States" limit="8"]

<!-- Show in 2 columns -->
[baum_company_list columns="2" limit="6"]

<!-- Filter by company size -->
[baum_company_list size="1001-5000" limit="5"]
```

### 3. `[baum_company_search]` - Interactive Company Search

Display a search form that allows visitors to search for companies.

#### Basic Usage
```
[baum_company_search]
```

#### Parameters
- **placeholder** - Search input placeholder: `Search companies...` (default)
- **button_text** - Search button text: `Search` (default)
- **results_per_page** - Number of results to show: `10` (default)

#### Examples
```
<!-- Custom search form -->
[baum_company_search placeholder="Find your company..." button_text="Find"]

<!-- Show more results -->
[baum_company_search results_per_page="20"]
```

## Company Profile Styles

### Card Style (Default) - New Clean Design
The card style displays a modern, iOS-inspired company profile with:
- **Black logo container** - Clean rounded background for logos
- **Company header** - Name with industry tags and stock symbols
- **Description section** - Auto-generated company description with "Read more" links
- **About section** - Clean data layout for employees, address, website, industry
- **Data visualization** - Sample earnings/performance bars
- **Modern styling** - 16px border radius, clean typography, proper spacing

### Minimal Style
The minimal style shows a compact display with:
- **Black logo container** - Consistent with card style
- **Company name** - Clean typography
- **Industry tag** - Optional industry information
- **Hover effects** - Subtle animations

### Detailed Style
Currently same as card style, but can be extended for more comprehensive information.

## Styling and Customization

### CSS Classes
The shortcodes generate HTML with specific CSS classes that you can style:

- `.baum-company-profile` - Main profile container
- `.baum-company-card` - Card style container
- `.baum-company-minimal` - Minimal style container
- `.baum-company-list` - Company list container
- `.baum-company-search` - Search form container
- `.baum-company-error` - Error message styling

### Custom CSS Example
```css
/* Customize company cards */
.baum-company-card {
    border-radius: 12px !important;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1) !important;
}

/* Change card hover effect */
.baum-company-card:hover {
    transform: scale(1.02) !important;
}

/* Style the search form */
.baum-company-search input {
    border-radius: 25px !important;
    padding: 12px 20px !important;
}
```

## Real-World Examples

### Company Showcase Page
```
<h2>Featured Technology Companies</h2>
[baum_company_list industry="technology" limit="6" columns="2"]

<h2>Spotlight: Apple Inc.</h2>
[baum_company name="Apple Inc." width="600px"]
```

### Company Directory
```
<h2>Find Companies</h2>
[baum_company_search placeholder="Search by company name..." results_per_page="15"]

<h3>Popular Companies</h3>
[baum_company_list limit="8" style="minimal" columns="2"]
```

### Industry Pages
```
<!-- Technology Companies -->
[baum_company_list industry="Computer Software" limit="10"]

<!-- Marketing Companies -->
[baum_company_list industry="Marketing" limit="8" columns="2"]
```

### Location-Based Listings
```
<!-- US Companies -->
[baum_company_list country="United States" limit="12" columns="3"]

<!-- UK Companies -->
[baum_company_list country="United Kingdom" limit="8"]
```

## Tips and Best Practices

1. **Performance**: Use reasonable limits (10-20) for company lists to maintain page load speed.

2. **Responsive Design**: The shortcodes are mobile-friendly, but test on different screen sizes.

3. **Caching**: Consider using caching plugins as database queries can be resource-intensive.

4. **Logo Quality**: Ensure your logo files are optimized for web display.

5. **Search SEO**: Use descriptive text around shortcodes to help with SEO.

6. **Error Handling**: The shortcodes gracefully handle missing companies or database errors.

## Troubleshooting

### Company Not Found
- Check the exact company name in your database
- Try partial name matches
- Use the database manager to verify company names

### No Results in Lists
- Check your filter criteria
- Verify data exists for the specified filters
- Check database connection

### Styling Issues
- Ensure your theme doesn't override plugin styles
- Use browser developer tools to inspect CSS conflicts
- Add `!important` to custom CSS if needed

### Performance Issues
- Reduce the number of companies displayed
- Use caching plugins
- Optimize your database (already indexed)

## Database Fields Available

The following company information is available for display:
- `name` - Company name
- `website` - Company website
- `industry` - Industry category
- `size` - Company size (employees)
- `founded` - Year founded
- `locality` - City
- `region` - State/Province
- `country` - Country
- `linkedin_url` - LinkedIn profile
- `logo_filename` - Logo file name

All shortcodes automatically handle missing data gracefully and only display available information.
